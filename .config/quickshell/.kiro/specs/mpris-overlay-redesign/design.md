# Design Document

## Overview

The MPRIS overlay redesign will transform the current media player interface into a visually distinctive, screen-responsive component with custom styling elements. The design emphasizes decorative borders, unique geometric shapes, and custom button designs while maintaining full functionality. The overlay will be specifically optimized for panel integration with dynamic sizing based on screen dimensions.

## Architecture

### MPRIS Service Integration
The redesign will integrate with a robust MPRIS service singleton that provides:
- **Reliable Player Tracking**: Automatic detection and switching between active players
- **Enhanced Error Handling**: Graceful fallbacks when players disconnect or fail
- **Simplified API**: Clean interface for play/pause, next/previous, and track information
- **Track Change Detection**: Proper handling of track transitions with animation support

### Screen Responsiveness System
The overlay will implement a dynamic sizing system that calculates dimensions based on the target screen properties. This will be achieved through:

- **Screen Detection**: Utilize `Quickshell.screens` to access screen properties
- **Dynamic Sizing**: Calculate overlay dimensions as percentages of screen size
- **Responsive Breakpoints**: Define different layouts for various screen size categories
- **Panel Integration**: Ensure proper positioning within panel boundaries

### Component Hierarchy
```
MprisOverlay (PanelWindow)
├── ScreenResponsiveContainer (Item)
│   ├── DoubleLineBorderBox (Custom Component)
│   │   ├── TitleSection (ColumnLayout)
│   │   │   ├── DecorativeTitleText (Custom Component)
│   │   │   └── ArtistText (Text)
│   │   ├── ContentSection (RowLayout)
│   │   │   ├── DiamondArtworkContainer (Custom Component)
│   │   │   └── ControlsSection (ColumnLayout)
│   │   │       ├── TrackInfoSection (ColumnLayout)
│   │   │       ├── SeekBarSection (existing)
│   │   │       └── ButtonControlsSection (RowLayout)
│   │   │           ├── TriangularNavButton (Custom Component)
│   │   │           ├── CircularPlayButton (existing, modified)
│   │   │           └── TriangularNavButton (Custom Component)
```

## Components and Interfaces

### 1. ScreenResponsiveContainer
**Purpose**: Handles dynamic sizing and screen adaptation
**Properties**:
- `targetScreen`: Reference to the screen object
- `baseWidth`: Calculated base width (e.g., screen.width * 0.25)
- `baseHeight`: Calculated base height (e.g., screen.height * 0.35)
- `scaleFactor`: Dynamic scaling factor based on screen DPI/size

**Interface**:
```qml
Item {
    property var targetScreen
    property real baseWidth: targetScreen ? targetScreen.width * 0.25 : 400
    property real baseHeight: targetScreen ? targetScreen.height * 0.35 : 280
    property real scaleFactor: calculateScaleFactor()
    
    function calculateScaleFactor() { /* implementation */ }
}
```

### 2. DoubleLineBorderBox
**Purpose**: Creates the decorative double-line border around the entire overlay
**Properties**:
- `outerBorderWidth`: Thickness of outer border line (default: 2)
- `innerBorderWidth`: Thickness of inner border line (default: 1)
- `borderSpacing`: Space between the two border lines (default: 2)
- `borderColor`: Color for both border lines

**Interface**:
```qml
Item {
    property int outerBorderWidth: 2
    property int innerBorderWidth: 1
    property int borderSpacing: 2
    property color borderColor: theme.border
    property alias content: contentArea.children
    
    // Outer border rectangle
    // Inner border rectangle
    // Content area with proper margins
}
```

### 3. DecorativeTitleText
**Purpose**: Renders track title with decorative 『 』 brackets
**Properties**:
- `titleText`: The track title to display
- `bracketStyle`: Styling for the decorative brackets
- `textStyle`: Styling for the title text

**Interface**:
```qml
Row {
    property string titleText
    property color bracketColor: theme.textAccent
    property color textColor: theme.textPrimary
    
    Text { text: "『" } // Left bracket
    Text { text: titleText } // Title
    Text { text: "』" } // Right bracket
}
```

### 4. DiamondArtworkContainer
**Purpose**: Displays album artwork in a diamond shape with double borders
**Properties**:
- `artworkUrl`: URL of the album artwork
- `diamondSize`: Size of the diamond container
- `outerBorderWidth`: Thickness of outer diamond border
- `innerBorderWidth`: Thickness of inner diamond border

**Interface**:
```qml
Item {
    property string artworkUrl
    property real diamondSize: 120
    property int outerBorderWidth: 2
    property int innerBorderWidth: 1
    property color borderColor: theme.border
    
    // Diamond-shaped clipping mask
    // Double border implementation
    // Image or placeholder content
}
```

### 5. TriangularNavButton
**Purpose**: Custom navigation buttons with double triangle design
**Properties**:
- `direction`: "previous" or "next"
- `enabled`: Button enabled state
- `triangleSymbol`: The double triangle symbol (＜ or ＞)

**Interface**:
```qml
Rectangle {
    property string direction: "next"
    property bool enabled: true
    property string triangleSymbol: direction === "next" ? "＞" : "＜"
    
    // Button styling
    // Mouse interaction
    // Visual feedback
}
```

## Data Models

### Screen Responsiveness Model
```javascript
{
    screenWidth: number,
    screenHeight: number,
    scaleFactor: number,
    overlayDimensions: {
        width: number,
        height: number,
        margins: {
            top: number,
            left: number,
            right: number,
            bottom: number
        }
    }
}
```

### Styling Configuration Model
```javascript
{
    borders: {
        outer: {
            width: number,
            color: string
        },
        inner: {
            width: number,
            color: string
        },
        spacing: number
    },
    diamond: {
        size: number,
        borderWidth: {
            outer: number,
            inner: number
        }
    },
    buttons: {
        triangular: {
            size: number,
            symbol: string
        },
        circular: {
            size: number,
            preserveExisting: boolean
        }
    }
}
```

## Error Handling

### Screen Detection Failures
- **Fallback Dimensions**: Use default dimensions if screen detection fails
- **Graceful Degradation**: Maintain functionality with static sizing
- **Error Logging**: Log screen detection issues for debugging

### Artwork Loading Failures
- **Placeholder Display**: Show default music icon in diamond container
- **Loading States**: Provide visual feedback during artwork loading
- **Error Recovery**: Handle network failures gracefully

### Component Rendering Issues
- **Fallback Styling**: Revert to simpler styling if custom components fail
- **Progressive Enhancement**: Ensure basic functionality works without decorative elements
- **Validation**: Validate all styling properties before application

## Testing Strategy

### Visual Regression Testing
1. **Screenshot Comparisons**: Capture overlay appearance across different screen sizes
2. **Component Isolation**: Test individual custom components separately
3. **Theme Compatibility**: Verify styling works with existing theme system

### Responsive Design Testing
1. **Multi-Screen Testing**: Test on various screen resolutions and DPI settings
2. **Scaling Validation**: Ensure proper scaling across different screen sizes
3. **Panel Integration**: Verify proper positioning within panel boundaries

### Functional Testing
1. **Button Interaction**: Test all button clicks and hover states
2. **Seek Bar Functionality**: Ensure seek bar continues to work properly
3. **MPRIS Integration**: Verify all existing MPRIS functionality is preserved

### Performance Testing
1. **Rendering Performance**: Measure impact of custom styling on render times
2. **Memory Usage**: Monitor memory consumption of new components
3. **Animation Smoothness**: Ensure animations remain smooth with new styling

### Cross-Platform Testing
1. **Wayland Compatibility**: Test on Wayland compositors
2. **Different Qt Versions**: Verify compatibility across Qt versions
3. **Font Rendering**: Test decorative characters across different font systems