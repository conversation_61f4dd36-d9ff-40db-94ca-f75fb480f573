# Design Document

## Overview

This design modifies the notification indicator in `notificationWidget.qml` to conditionally apply visual decorations based on the notification state. The indicator will show minimal styling when no notifications are present while maintaining full functionality.

## Architecture

The solution involves modifying the existing `Rectangle` component that serves as the notification indicator to conditionally apply styling properties based on the `hasNotifications` boolean property.

## Components and Interfaces

### Modified Component: Notification Indicator Rectangle

**Location:** `bar/modules/notificationModules/notificationWidget.qml`
**Component:** `Rectangle` with id `indicator`

**Current Properties:**
- `color`: Always shows background color based on notification state
- `border.width`: Always shows border (1px)
- `border.color`: Always shows border color

**New Conditional Properties:**
- `color`: Transparent when no notifications, themed when has notifications
- `border.width`: 0 when no notifications, 1 when has notifications  
- `border.color`: Transparent when no notifications, themed when has notifications

### State Logic

The indicator will use the existing `hasNotifications` property to determine styling:

```qml
Rectangle {
    id: indicator
    // ... existing size properties
    
    // Conditional styling based on notification state
    color: hasNotifications ? (theme.accent) : "transparent"
    border.width: hasNotifications ? 1 : 0
    border.color: hasNotifications ? theme.border : "transparent"
    
    // ... rest of component unchanged
}
```

## Data Models

No changes to data models are required. The existing `hasNotifications` boolean property will drive the conditional styling.

## Error Handling

No additional error handling is needed as this change only affects visual styling using existing, well-tested properties.

## Testing Strategy

### Visual Testing
1. **No Notifications State**: Verify indicator shows only "○" symbol without background or border
2. **With Notifications State**: Verify indicator maintains current styled appearance
3. **State Transitions**: Verify smooth transitions between states using existing animations
4. **Theme Consistency**: Verify colors match theme when notifications are present

### Functional Testing
1. **Click Behavior**: Verify left/right click functionality remains unchanged
2. **Hover Effects**: Verify hover animations still work properly
3. **Overlay Integration**: Verify notification overlay still opens correctly
4. **Notification Management**: Verify clearing notifications properly updates indicator state

### Integration Testing
1. **Bar Layout**: Verify indicator positioning remains consistent
2. **Animation Continuity**: Verify existing width/height animations still work
3. **Theme Integration**: Verify theme color changes are properly applied

## Implementation Notes

- The change is minimal and only affects the visual styling properties
- All existing functionality, animations, and interactions remain unchanged
- The modification leverages existing theme colors and animation behaviors
- No new dependencies or complex logic is introduced