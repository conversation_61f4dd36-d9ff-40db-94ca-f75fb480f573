# Design Document

## Overview

This design overhauls the NotificationOverlay.qml to create a full-width panel that appears below the bar, spanning the screen width with proper margins. The overlay will display up to 10 notifications with consistent theming matching other overlays in the system.

## Architecture

The solution modifies the existing `NotificationOverlay.qml` component to:
1. Calculate screen dimensions and position the overlay as a full-width panel
2. Implement proper theming with rounded rectangles and theme colors
3. Limit notification display to 10 items with proper scrolling
4. Enhance notification detail display with better formatting

## Components and Interfaces

### Modified Component: NotificationOverlay

**Location:** `bar/modules/notificationModules/NotificationOverlay.qml`

**Key Changes:**

#### Positioning and Sizing
```qml
// Current positioning (right-aligned, fixed width)
anchors {
    right: true
    top: true
}
width: 400

// New positioning (full-width panel below bar)
anchors {
    left: true
    right: true
    top: true
}
margins {
    left: 20
    right: 20
    top: barHeight + 10  // Position below bar
}
```

#### Screen Dimension Calculation
- Use `screen.geometry.width` for width calculation
- Calculate bar height dynamically or use fixed value (30px + margins)
- Apply consistent margins (20px left/right)

#### Notification Limiting
```qml
ListView {
    model: notificationWidget ? 
           notificationWidget.activeNotifications.slice(0, 10) : []
}
```

### Theming Improvements

#### Background Styling
```qml
Rectangle {
    anchors.fill: parent
    radius: theme.largeBorderRadius  // Consistent with other overlays
    color: theme.background
    border.width: 1
    border.color: theme.border
}
```

#### Notification Item Styling
- Use `theme.backgroundSecondary` for notification items
- Apply `theme.borderRadius` for rounded corners
- Consistent spacing and padding throughout

### Layout Structure

```
NotificationOverlay (PanelWindow)
├── Background Rectangle (rounded, themed)
│   ├── Header Section
│   │   ├── Title ("Notifications")
│   │   ├── Count Badge
│   │   └── Clear Button
│   └── Content Section
│       └── ListView (max 10 items)
│           └── NotificationItem (repeated)
│               ├── Icon/Avatar
│               ├── Content Column
│               │   ├── Title + Timestamp
│               │   ├── Body Text
│               │   └── App Name
│               └── Dismiss Action
```

## Data Models

### Notification Data Structure
Each notification item will display:
- `summary`: Notification title (bold, primary text color)
- `body`: Notification content (secondary text color, multi-line with ellipsis)
- `appName`: Source application (tertiary text color, italic)
- `timestamp`: Relative time display (tertiary text color)

### Notification Limiting Logic
```javascript
function getLimitedNotifications() {
    if (!notificationWidget || !notificationWidget.activeNotifications) {
        return []
    }
    return notificationWidget.activeNotifications.slice(0, 10)
}
```

## Error Handling

### Screen Dimension Fallbacks
- If screen dimensions are unavailable, fallback to reasonable defaults
- Minimum width: 400px, Maximum width: 1200px
- Graceful handling of screen changes/rotations

### Notification Data Validation
- Handle missing notification properties gracefully
- Provide fallback text for empty fields
- Validate timestamp data before formatting

## Testing Strategy

### Visual Testing
1. **Full-width Display**: Verify overlay spans screen width with proper margins
2. **Positioning**: Confirm overlay appears below bar at correct position
3. **Theming Consistency**: Verify colors and styling match other overlays
4. **Notification Limiting**: Test with more than 10 notifications

### Responsive Testing
1. **Different Screen Sizes**: Test on various screen resolutions
2. **Dynamic Resizing**: Verify behavior when screen dimensions change
3. **Content Overflow**: Test with long notification text and many notifications

### Interaction Testing
1. **Click to Dismiss**: Verify individual notification removal
2. **Clear All**: Test bulk notification clearing
3. **Outside Click**: Confirm overlay closes when clicking outside
4. **Scrolling**: Test ListView scrolling with many notifications

## Implementation Notes

### Screen Dimension Access
- Use `screen.geometry.width` and `screen.geometry.height` for calculations
- Consider bar height (30px) plus margins for top positioning
- Apply consistent horizontal margins (20px each side)

### Performance Considerations
- Limit ListView model to 10 items to prevent performance issues
- Use efficient delegate recycling for smooth scrolling
- Implement proper cleanup when overlay is hidden

### Accessibility
- Maintain keyboard navigation support
- Ensure proper focus management
- Use semantic colors and sufficient contrast ratios