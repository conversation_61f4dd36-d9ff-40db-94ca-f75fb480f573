# Implementation Plan

- [x] 1. Update overlay positioning and dimensions for full-width panel
  - Modify `anchors` to use left/right instead of right-only positioning
  - Calculate screen-based width with proper margins (20px each side)
  - Position overlay below bar using calculated top margin (bar height + spacing)
  - Remove fixed width and use screen-responsive sizing
  - _Requirements: 1.1, 1.2, 1.3, 1.4_

- [x] 2. Implement notification limiting to maximum 10 items
  - Modify ListView model to slice activeNotifications array to first 10 items
  - Add proper handling for empty notification state
  - Ensure ListView performance with limited item count
  - _Requirements: 2.1, 2.2_

- [x] 3. Apply consistent theming with rounded rectangles and proper colors
  - Update background Rectangle to use theme.largeBorderRadius for consistency
  - Replace any hardcoded colors with theme properties
  - Ensure notification items use theme.backgroundSecondary and proper borders
  - Apply consistent spacing and padding throughout the overlay
  - _Requirements: 2.3, 2.4_

- [x] 4. Enhance notification detail display and formatting
  - Improve notification item layout with proper text hierarchy
  - Ensure title, body, and app name are clearly differentiated
  - Implement proper text truncation with ellipsis for long content
  - Format timestamps to show relative time appropriately
  - _Requirements: 3.1, 3.2, 3.3, 3.4_

- [x] 5. Test overlay functionality and interactions
  - Verify click-to-dismiss works for individual notifications
  - Test clear all functionality
  - Confirm outside-click-to-close behavior
  - Validate overlay positioning on different screen sizes
  - _Requirements: 4.1, 4.2, 4.3, 4.4_