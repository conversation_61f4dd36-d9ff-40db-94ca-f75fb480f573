# Requirements Document

## Introduction

This feature overhauls the notification history overlay to create a proper full-width panel that appears below the bar, spanning the screen width with proper margins, displaying up to 10 notifications with consistent theming and improved layout.

## Requirements

### Requirement 1

**User Story:** As a user, I want the notification history overlay to span the full width of the screen below the bar, so that I have a proper panel-like interface for viewing notifications.

#### Acceptance Criteria

1. WHEN the notification overlay is opened THEN it SHALL span the full width of the screen with appropriate margins
2. WHEN the overlay is positioned THEN it SHALL appear directly below the bar with proper spacing
3. WHEN calculating dimensions THEN the overlay SHALL use screen dimensions for width calculation
4. WHEN displaying the overlay THEN it SHALL maintain consistent positioning across different screen sizes

### Requirement 2

**User Story:** As a user, I want the notification history to display up to 10 notifications with proper theming, so that I can view recent notifications in a clean, consistent interface.

#### Acceptance Criteria

1. WHEN displaying notifications THEN the overlay SHALL show a maximum of 10 notifications
2. WHEN no notifications exist THEN the overlay SHALL display an appropriate empty state message
3. WHEN notifications are displayed THEN they SHALL use the same theming as other overlays (rounded rectangles, proper colors)
4. WHEN the overlay background is rendered THEN it SHALL use a rounded rectangle with theme colors instead of a plain white background

### Requirement 3

**User Story:** As a user, I want each notification in the history to show detailed preview information, so that I can quickly understand the notification content without opening individual items.

#### Acceptance Criteria

1. WHEN displaying a notification THEN it SHALL show the notification title, body text, and app name
2. WHEN displaying notification details THEN the text SHALL be properly formatted with appropriate font sizes and colors
3. WHEN notification content is too long THEN it SHALL be truncated with ellipsis
4. WHEN displaying timestamps THEN they SHALL show relative time (e.g., "5m ago", "2h ago")

### Requirement 4

**User Story:** As a user, I want to interact with the notification history overlay, so that I can clear notifications and close the overlay when done.

#### Acceptance Criteria

1. WHEN clicking on a notification THEN it SHALL be removed from the history
2. WHEN clicking the clear button THEN all notifications SHALL be cleared
3. WHEN clicking outside the overlay THEN the overlay SHALL close
4. WHEN the overlay is closed THEN it SHALL properly hide and clean up resources