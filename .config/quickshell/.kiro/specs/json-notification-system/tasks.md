# Implementation Plan

- [x] 1. Create the NotificationManager singleton component
  - Create new file `bar/modules/notificationModules/NotificationManager.qml` with singleton pragma
  - Implement basic component structure with required imports and singleton setup
  - Define core properties for notification list, popup management, and file path
  - Add signal definitions for initDone, notify, discard, discardAll, and timeout
  - _Requirements: 5.1, 5.2, 5.3, 5.4, 5.5_

- [x] 2. Implement Notif and NotifTimer component definitions
  - Create Notif component with all required properties (notificationId, actions, appIcon, etc.)
  - Implement notifToJSON and notifToString utility functions for serialization
  - Create NotifTimer component with automatic timeout and cleanup functionality
  - Add proper property bindings and change handlers for notification lifecycle
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 3.1, 3.2, 3.3_

- [x] 3. Implement JSON file persistence with FileView integration
  - Add FileView component for JSON file operations at specified file path
  - Implement file loading with proper error handling for missing and corrupted files
  - <PERSON>reate stringifyList function to convert notification list to JSON format
  - Add automatic file saving when notification list changes
  - Handle FileViewError.FileNotFound by creating new empty file
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5_

- [x] 4. Create notification grouping and sorting system
  - Implement groupsForList function to group notifications by application name
  - Create appNameListForGroups function for time-based sorting of groups
  - Add latestTimeForApp tracking with automatic updates on list changes
  - Implement computed properties for groupsByAppName, appNameList, and popup variants
  - Add proper cleanup of latestTimeForApp when apps no longer have notifications
  - _Requirements: 4.1, 4.2, 4.3, 4.4, 4.5_

- [x] 5. Integrate NotificationServer and implement notification reception
  - Add NotificationServer component with proper configuration (actions, markup, images supported)
  - Implement onNotification handler to create Notif objects from incoming notifications
  - Add ID offset management to prevent collision between persisted and new notifications
  - Create popup logic with inhibition checking and timer creation
  - Emit notify signal and update file storage when new notifications arrive
  - _Requirements: 2.1, 2.2, 2.3, 3.1, 3.2, 3.4, 5.2_

- [x] 6. Implement notification management functions
  - Create discardNotification function to remove notifications by ID from list and server
  - Implement discardAllNotifications function to clear all notifications and dismiss from server
  - Add timeoutNotification function to remove popup state while keeping in history
  - Create timeoutAll function to remove popup state from all current popups
  - Ensure all functions emit appropriate signals and update file storage
  - _Requirements: 3.5, 4.5, 5.2, 5.4_

- [x] 7. Implement notification action handling
  - Create attemptInvokeAction function to find and invoke notification actions
  - Add proper error handling for missing notifications or invalid actions
  - Implement action lookup in NotificationServer's tracked notifications
  - Ensure notification is dismissed after action invocation
  - Add comprehensive logging for action invocation debugging
  - _Requirements: 2.2, 2.3_

- [x] 8. Add initialization and component lifecycle management
  - Implement Component.onCompleted to trigger file loading and initialization
  - Add proper ID offset calculation based on maximum existing notification ID
  - Emit initDone signal after successful initialization
  - Create refresh function to reload notifications from file
  - Add triggerListChange utility function for reactive list updates
  - _Requirements: 1.2, 5.3, 5.4_

- [x] 9. Update existing notification components to use NotificationManager
  - Modify NotificationOverlay.qml to import and use NotificationManager singleton
  - Replace local notification storage with NotificationManager.list access
  - Update notification display to use grouped data from NotificationManager
  - Connect to NotificationManager signals for reactive updates
  - Remove redundant notification handling code from overlay component
  - _Requirements: 5.1, 5.2, 5.5_

- [x] 10. Create comprehensive unit tests for NotificationManager
  - Write tests for JSON serialization and deserialization functions
  - Test notification grouping and sorting logic with various data sets
  - Verify popup timeout behavior and timer cleanup
  - Test file persistence across component destruction and recreation
  - Add tests for error handling scenarios (corrupted files, missing notifications)
  - _Requirements: 1.1, 1.5, 3.3, 4.1, 4.2_