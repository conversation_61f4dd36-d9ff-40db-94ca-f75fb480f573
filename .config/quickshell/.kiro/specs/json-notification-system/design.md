# Design Document

## Overview

This design implements a comprehensive JSON-based notification management system using a singleton architecture. The system replaces the current in-memory notification handling with persistent storage, enhanced notification features, popup management with timeouts, and improved notification grouping by application.

## Architecture

The solution introduces a new singleton component `NotificationManager.qml` that serves as the central hub for all notification operations. This manager handles:

1. **Persistent Storage**: JSON file-based storage for notification persistence across restarts
2. **Rich Notifications**: Support for images, actions, and enhanced metadata
3. **Popup Management**: Automatic popup display with configurable timeouts
4. **Application Grouping**: Intelligent grouping and sorting of notifications by application
5. **Signal-based Communication**: Event-driven architecture for component communication

### Core Architecture Components

```
NotificationManager (Singleton)
├── NotificationServer (Quickshell integration)
├── FileView (JSON persistence)
├── Notification Objects (Notif components)
├── Timer Objects (NotifTimer components)
└── Signal Interface (notify, discard, timeout, etc.)
```

## Components and Interfaces

### Primary Component: NotificationManager.qml

**Location:** `bar/modules/notificationModules/NotificationManager.qml`

This singleton component will be created as a new file that centralizes all notification management.

#### Key Properties

```qml
// Core state
property bool silent: false
property var filePath: Directories.notificationsPath
property list<Notif> list: []
property var popupList: list.filter((notif) => notif.popup)
property bool popupInhibited: (GlobalStates?.sidebarRightOpen ?? false) || silent

// Grouping and organization
property var latestTimeForApp: ({})
property var groupsByAppName: groupsForList(root.list)
property var popupGroupsByAppName: groupsForList(root.popupList)
property var appNameList: appNameListForGroups(root.groupsByAppName)
property var popupAppNameList: appNameListForGroups(root.popupGroupsByAppName)

// ID management for collision avoidance
property int idOffset
```

#### Signal Interface

```qml
signal initDone()
signal notify(notification: var)
signal discard(id: int)
signal discardAll()
signal timeout(id: var)
```

### Notification Data Model

#### Notif Component Structure

```qml
component Notif: QtObject {
    required property int notificationId
    property Notification notification
    property list<var> actions: notification?.actions.map((action) => ({
        "identifier": action.identifier,
        "text": action.text,
    })) ?? []
    property bool popup: false
    property string appIcon: notification?.appIcon ?? ""
    property string appName: notification?.appName ?? ""
    property string body: notification?.body ?? ""
    property string image: notification?.image ?? ""
    property string summary: notification?.summary ?? ""
    property double time
    property string urgency: notification?.urgency.toString() ?? "normal"
    property Timer timer
}
```

#### JSON Serialization Format

```json
{
  "notificationId": 123,
  "actions": [
    {"identifier": "action1", "text": "Action Text"}
  ],
  "appIcon": "/path/to/icon.png",
  "appName": "Application Name",
  "body": "Notification body text",
  "image": "/path/to/image.png",
  "summary": "Notification title",
  "time": 1643723400000,
  "urgency": "normal"
}
```

### Popup Management System

#### NotifTimer Component

```qml
component NotifTimer: Timer {
    required property int notificationId
    interval: 5000
    running: true
    onTriggered: () => {
        root.timeoutNotification(notificationId)
        destroy()
    }
}
```

#### Popup Logic Flow

1. **New Notification Received**
   - Check if popups are inhibited
   - If not inhibited, set `popup: true`
   - Create timer based on `expireTimeout` or default 5000ms
   - Timer automatically removes popup state when triggered

2. **Popup Inhibition States**
   - Sidebar open: `GlobalStates?.sidebarRightOpen`
   - Silent mode: `silent` property
   - Manual inhibition through API

### Application Grouping System

#### Group Data Structure

```javascript
{
  "appName": "Application Name",
  "appIcon": "/path/to/icon.png",
  "notifications": [/* array of Notif objects */],
  "time": 1643723400000  // Latest notification time in group
}
```

#### Grouping Functions

```javascript
function groupsForList(list) {
    const groups = {}
    list.forEach((notif) => {
        if (!groups[notif.appName]) {
            groups[notif.appName] = {
                appName: notif.appName,
                appIcon: notif.appIcon,
                notifications: [],
                time: 0
            }
        }
        groups[notif.appName].notifications.push(notif)
        groups[notif.appName].time = latestTimeForApp[notif.appName] || notif.time
    })
    return groups
}

function appNameListForGroups(groups) {
    return Object.keys(groups).sort((a, b) => {
        return groups[b].time - groups[a].time  // Sort by time, descending
    })
}
```

## Data Models

### File Storage Model

#### File Location
- Path: `Directories.notificationsPath` (typically `~/.config/quickshell/notifications.json`)
- Format: JSON array of notification objects
- Encoding: UTF-8

#### File Operations

```javascript
// Save notifications to file
function saveToFile() {
    const jsonData = JSON.stringify(
        root.list.map((notif) => notifToJSON(notif)), 
        null, 
        2
    )
    notifFileView.setText(jsonData)
}

// Load notifications from file
function loadFromFile() {
    const fileContents = notifFileView.text()
    const notifications = JSON.parse(fileContents)
    // Reconstruct Notif objects from JSON data
}
```

### ID Management System

#### Collision Avoidance
- Quickshell notification IDs start at 1 on each run
- Persisted notifications may have higher IDs
- `idOffset` property ensures no ID collisions
- New notifications get ID: `notification.id + idOffset`

## Error Handling

### File System Errors

```javascript
onLoadFailed: (error) => {
    if(error == FileViewError.FileNotFound) {
        console.log("[Notifications] File not found, creating new file.")
        root.list = []
        notifFileView.setText(stringifyList(root.list))
    } else {
        console.log("[Notifications] Error loading file: " + error)
        // Fallback to empty state
        root.list = []
    }
}
```

### Notification Server Errors
- Handle missing notification properties gracefully
- Provide fallback values for required fields
- Validate notification data before processing
- Log errors for debugging without crashing

### Action Invocation Errors
- Verify notification exists in server before invoking actions
- Handle cases where sender application is no longer running
- Gracefully fail action invocation with appropriate logging
- Always dismiss notification after action attempt

## Testing Strategy

### Persistence Testing
1. **File Creation**: Verify JSON file is created when it doesn't exist
2. **Data Persistence**: Confirm notifications survive application restart
3. **File Corruption**: Test graceful handling of corrupted JSON files
4. **Large Datasets**: Test performance with many notifications

### Popup Management Testing
1. **Timeout Behavior**: Verify popups disappear after specified timeout
2. **Inhibition States**: Test popup blocking when sidebar is open or silent mode
3. **Timer Cleanup**: Ensure timers are properly destroyed after triggering
4. **Multiple Popups**: Test behavior with multiple simultaneous popups

### Grouping and Sorting Testing
1. **Application Grouping**: Verify notifications are correctly grouped by app
2. **Time-based Sorting**: Confirm groups are sorted by latest notification time
3. **Dynamic Updates**: Test grouping updates when notifications are added/removed
4. **Empty Groups**: Handle edge cases with empty or single-notification groups

### Integration Testing
1. **Singleton Access**: Verify singleton behavior across multiple components
2. **Signal Propagation**: Test that all signals are properly emitted and received
3. **Quickshell Integration**: Confirm compatibility with NotificationServer
4. **UI Component Integration**: Test integration with existing overlay components

## Implementation Notes

### Migration Strategy
1. **Backward Compatibility**: Ensure existing notification components continue to work
2. **Gradual Migration**: Allow incremental adoption of new notification manager
3. **Data Migration**: Convert existing in-memory notifications to JSON format
4. **API Compatibility**: Maintain existing function signatures where possible

### Performance Considerations
- **Lazy Loading**: Only load notifications when needed
- **Memory Management**: Properly destroy timer objects after use
- **File I/O Optimization**: Batch file writes to reduce disk operations
- **List Operations**: Use efficient array operations for large notification lists

### Security Considerations
- **File Permissions**: Ensure notification file has appropriate read/write permissions
- **Data Validation**: Validate all notification data before processing
- **Path Traversal**: Prevent malicious file path manipulation
- **Resource Limits**: Implement reasonable limits on notification count and size