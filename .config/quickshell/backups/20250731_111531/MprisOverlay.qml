import QtQuick
import QtQuick.Layouts
import Quickshell
import Quickshell.Wayland
import Quickshell.Services.Mpris
import Quickshell.Widgets
import "../"

PanelWindow {
    id: mprisOverlay

    property var screen
    property var barWindow
    property bool overlayVisible: false
    property int overlayWidth: 400
    property int overlayHeight: 280

    signal overlayHidden()
    signal overlayShown()
    signal trackChanged(string title, string artist, string album)
    signal playbackStateChanged(int state)

    readonly property var activePlayer: {
        if (!Mpris.players || !Mpris.players.values) {
            return null
        }

        var playingPlayer = null
        var pausedPlayer = null
        var anyPlayer = null

        try {
            var playersList = Mpris.players.values

            for (var i = 0; i < playersList.length; i++) {
                var player = playersList[i]

                if (player && typeof player === "object" && player.identity) {
                    if (!anyPlayer) anyPlayer = player  
                    if (player.playbackState === MprisPlaybackState.Playing) {
                        playingPlayer = player
                    } else if (player.playbackState === MprisPlaybackState.Paused) {
                        pausedPlayer = player
                    }
                }
            }
        } catch (e) {
            console.warn("Error detecting MPRIS players in overlay:", e)
        }

        return playingPlayer || pausedPlayer || anyPlayer
    }

    property var previousPlayer: null
    
    // Timer to handle track change delays
    Timer {
        id: trackChangeTimer
        interval: 200 // Small delay to allow track info to stabilize
        onTriggered: {
            if (activePlayer) {
                console.log("Track change timer triggered - updating display")
                console.log("Final position:", activePlayer.position, "Length:", activePlayer.length)
                // Force update of time displays
                currentTimeText.text = Qt.binding(function() {
                    if (!activePlayer) return "0:00"
                    var pos = activePlayer.position || 0
                    return formatTime(pos)
                })
                totalTimeText.text = Qt.binding(function() {
                    if (!activePlayer) return "0:00"
                    var len = activePlayer.length || 0
                    return formatTime(len)
                })
            }
        }
    }

    onActivePlayerChanged: {
        console.log("activePlayer changed from", previousPlayer ? previousPlayer.identity : "null", "to", activePlayer ? activePlayer.identity : "null")
        
        if (activePlayer !== previousPlayer) {
            previousPlayer = activePlayer
            if (activePlayer) {
                console.log("=== MPRIS Player Debug Info ===")
                console.log("Player identity:", activePlayer.identity)
                console.log("Position:", activePlayer.position, "Length:", activePlayer.length)
                console.log("Track:", activePlayer.trackTitle)
                console.log("Properties:")
                console.log("  - canPlay:", activePlayer.canPlay)
                console.log("  - canPause:", activePlayer.canPause)
                console.log("  - canTogglePlaying:", activePlayer.canTogglePlaying)
                console.log("  - canGoNext:", activePlayer.canGoNext)
                console.log("  - canGoPrevious:", activePlayer.canGoPrevious)
                console.log("  - canSeek:", activePlayer.canSeek)
                console.log("  - playbackState:", activePlayer.playbackState)
                console.log("===============================")
                
                // Connect to position and length change signals for new tracks
                if (activePlayer.positionChanged) {
                    activePlayer.positionChanged.connect(function() {
                        console.log("Position updated:", activePlayer.position, "Length:", activePlayer.length)
                    })
                }
                
                // Start timer to handle potential track change delays
                trackChangeTimer.restart()
                
                trackChanged(
                    activePlayer.trackTitle || "",
                    activePlayer.trackArtist || "",
                    activePlayer.trackAlbum || ""
                )
                playbackStateChanged(activePlayer.playbackState)
            } else {
                console.log("No active MPRIS player found")
            }
        }
    }

    // Add component completed for debugging
    Component.onCompleted: {
        console.log("MprisOverlay component loaded")
        console.log("Available MPRIS players:", Mpris.players ? Object.keys(Mpris.players.values || {}).length : 0)
    }

    screen: Quickshell.screens[0]
    visible: overlayVisible && activePlayer !== null
    color: "transparent"
    implicitWidth: overlayWidth
    implicitHeight: overlayHeight

    anchors {
        top: true
        left: false
        right: false
        bottom: false
    }
    
    margins {
        top: 8
        left: 4
        right: 4
        bottom: 0
    }

    WlrLayershell.layer: WlrLayer.Top
    WlrLayershell.exclusiveZone: 0

    Theme {
        id: theme
    }

    // Functions defined at the bottom with logging

    function formatTime(timeValue) {
        // Handle null, undefined, or zero values
        if (timeValue === null || timeValue === undefined || timeValue <= 0) {
            return "0:00"
        }
        
        var seconds
        
        // More robust detection for different time formats
        // MPRIS spec uses microseconds, but some players (especially browsers) may use different formats
        if (timeValue > 3600) {
            // If the value is greater than 1 hour in seconds (3600), it's likely microseconds
            // This handles cases where browsers send microsecond values
            seconds = Math.floor(timeValue / 1000000)
        } else if (timeValue > 1000 && timeValue < 3600) {
            // Values between 1000-3600 could be milliseconds for shorter tracks
            // Check if it makes sense as milliseconds by seeing if it's a reasonable duration
            var asMilliseconds = Math.floor(timeValue / 1000)
            var asSeconds = Math.floor(timeValue)
            
            // If the millisecond interpretation gives us a reasonable duration (< 1 hour)
            // and the seconds interpretation would be very long, use milliseconds
            if (asMilliseconds < 3600 && asSeconds > 3600) {
                seconds = asMilliseconds
            } else {
                seconds = asSeconds
            }
        } else {
            // For values <= 1000, treat as seconds (most common for short durations)
            seconds = Math.floor(timeValue)
        }
        
        // Handle edge cases
        if (seconds < 0) seconds = 0
        if (seconds > 86400) { // More than 24 hours, likely a format issue
            console.warn("Unusually long duration detected:", timeValue, "-> treating as microseconds")
            seconds = Math.floor(timeValue / 1000000)
        }
        
        var mins = Math.floor(seconds / 60)
        var secs = seconds % 60
        
        // For very long durations, show hours
        if (mins >= 60) {
            var hours = Math.floor(mins / 60)
            mins = mins % 60
            return hours + ":" + (mins < 10 ? "0" : "") + mins + ":" + (secs < 10 ? "0" : "") + secs
        }
        
        return mins + ":" + (secs < 10 ? "0" : "") + secs
    }

    Item {
        id: mprisContainer
        anchors.fill: parent

        Rectangle {
            id: backgroundRect
            anchors.fill: parent
            color: theme.background
            radius: theme.largeBorderRadius
            opacity: overlayVisible ? 1.0 : 0.0

            Behavior on opacity {
                NumberAnimation { 
                    duration: theme.slowAnimationDuration
                    easing.type: Easing.OutCubic 
                }
            }
        }

        ColumnLayout {
            anchors.fill: parent
            anchors.margins: 20
            anchors.topMargin: 28
            spacing: 16

            Text {
                text: activePlayer ? (activePlayer.identity || "Media Player") : "No Player"
                color: theme.textSecondary
                font.pixelSize: 12
                font.family: "JetBrains Mono, monospace"
                font.weight: Font.Medium
                Layout.alignment: Qt.AlignHCenter
            }

            RowLayout {
                Layout.fillWidth: true
                Layout.fillHeight: true
                spacing: 20

                Rectangle {
                    Layout.preferredWidth: 120
                    Layout.preferredHeight: 120
                    radius: theme.borderRadius
                    color: theme.backgroundTertiary
                    border.color: theme.border
                    border.width: 1

                    Rectangle {
                        anchors.fill: parent
                        anchors.margins: 2
                        radius: theme.smallBorderRadius
                        clip: true
                        visible: activePlayer && activePlayer.trackArtUrl

                        Image {
                            anchors.fill: parent
                            source: activePlayer && activePlayer.trackArtUrl ? activePlayer.trackArtUrl : ""
                            fillMode: Image.PreserveAspectCrop
                        }
                    }

                    Text {
                        anchors.centerIn: parent
                        text: "󰝚"
                        color: theme.textSecondary
                        font.pixelSize: 32
                        font.family: "JetBrains Mono Nerd Font, monospace"
                        visible: !activePlayer || !activePlayer.trackArtUrl
                    }
                }

                ColumnLayout {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    spacing: 12

                    ColumnLayout {
                        Layout.fillWidth: true
                        spacing: 4

                        Text {
                            text: activePlayer ? (activePlayer.trackTitle || "Unknown Title") : "No Track"
                            color: theme.textPrimary
                            font.pixelSize: 16
                            font.family: "JetBrains Mono, monospace"
                            font.weight: Font.Bold
                            elide: Text.ElideRight
                            Layout.fillWidth: true
                        }

                        Text {
                            text: activePlayer ? (activePlayer.trackArtist || "Unknown Artist") : "Unknown Artist"
                            color: theme.textSecondary
                            font.pixelSize: 14
                            font.family: "JetBrains Mono, monospace"
                            elide: Text.ElideRight
                            Layout.fillWidth: true
                        }

                        Text {
                            text: activePlayer ? (activePlayer.trackAlbum || "") : ""
                            color: theme.textTertiary
                            font.pixelSize: 12
                            font.family: "JetBrains Mono, monospace"
                            elide: Text.ElideRight
                            Layout.fillWidth: true
                            visible: text !== ""
                        }
                    }

                    ColumnLayout {
                        Layout.fillWidth: true
                        spacing: 4
                        visible: activePlayer !== null

                        Item {
                            Layout.fillWidth: true
                            Layout.maximumWidth: parent.width
                            height: 20
                            clip: true // Ensure content doesn't overflow
                            
                            Rectangle {
                                id: progressTrack
                                anchors.left: parent.left
                                anchors.right: parent.right
                                anchors.verticalCenter: parent.verticalCenter
                                height: 4
                                radius: 2
                                color: theme.backgroundTertiary
                                
                                Rectangle {
                                    id: progressFill
                                    anchors.left: parent.left
                                    anchors.top: parent.top
                                    anchors.bottom: parent.bottom
                                    width: {
                                        if (!activePlayer || !activePlayer.length || activePlayer.length <= 0) {
                                            return 0
                                        }
                                        var ratio = Math.max(0, Math.min(1, (activePlayer.position || 0) / activePlayer.length))
                                        return ratio * progressTrack.width
                                    }
                                    radius: parent.radius
                                    color: theme.accent

                                    Behavior on width {
                                        enabled: !seekMouseArea.pressed
                                        NumberAnimation { 
                                            duration: theme.fastAnimationDuration
                                            easing.type: Easing.OutCubic 
                                        }
                                    }
                                }
                                
                                // Timer to manually trigger position updates for smooth progress bar
                                Timer {
                                    id: positionUpdateTimer
                                    interval: 100 // Update every 100ms for smooth animation
                                    running: overlayVisible && activePlayer && activePlayer.playbackState === MprisPlaybackState.Playing
                                    repeat: true
                                    onTriggered: {
                                        if (activePlayer) {
                                            activePlayer.positionChanged()
                                        }
                                    }
                                }
                                
                                MouseArea {
                                    id: seekMouseArea
                                    anchors.fill: parent
                                    enabled: activePlayer && activePlayer.canSeek
                                    preventStealing: true
                                    
                                    onPressed: function(mouse) {
                                        console.log("Seekbar pressed at x:", mouse.x, "track width:", progressTrack.width)
                                        seekToPosition(mouse.x)
                                    }
                                    
                                    onPositionChanged: function(mouse) {
                                        if (pressed) {
                                            console.log("Seekbar dragging at x:", mouse.x)
                                            seekToPosition(mouse.x)
                                        }
                                    }
                                    
                                    onReleased: function(mouse) {
                                        console.log("Seekbar released")
                                    }
                                    
                                    function seekToPosition(x) {
                                        console.log("seekToPosition called with x:", x)
                                        if (activePlayer && activePlayer.length > 0 && progressTrack.width > 0) {
                                            // Ensure x is within bounds
                                            var boundedX = Math.max(0, Math.min(x, progressTrack.width))
                                            var ratio = boundedX / progressTrack.width
                                            var targetPosition = ratio * activePlayer.length
                                            var currentPosition = activePlayer.position || 0
                                            var offset = targetPosition - currentPosition
                                            
                                            console.log("Seeking - boundedX:", boundedX, "ratio:", ratio, "targetPosition:", targetPosition, "currentPosition:", currentPosition, "offset:", offset)
                                            
                                            try {
                                                // Use seek with offset, not absolute position
                                                activePlayer.seek(offset)
                                                console.log("Seek successful with offset:", offset)
                                            } catch (e) {
                                                console.log("Seek failed:", e)
                                            }
                                        } else {
                                            console.log("Cannot seek - activePlayer:", !!activePlayer, "length:", activePlayer ? activePlayer.length : "null", "track width:", progressTrack.width)
                                        }
                                    }
                                }
                            }
                        }

                        RowLayout {
                            Layout.fillWidth: true

                            Text {
                                id: currentTimeText
                                text: {
                                    if (!activePlayer) return "0:00"
                                    var pos = activePlayer.position || 0
                                    console.log("Current position raw value:", pos, "formatted:", formatTime(pos))
                                    return formatTime(pos)
                                }
                                color: theme.textTertiary
                                font.pixelSize: 10
                                font.family: "JetBrains Mono, monospace"
                            }

                            Item { Layout.fillWidth: true }

                            Text {
                                id: totalTimeText
                                text: {
                                    if (!activePlayer) return "0:00"
                                    var len = activePlayer.length || 0
                                    console.log("Total length raw value:", len, "formatted:", formatTime(len))
                                    return formatTime(len)
                                }
                                color: theme.textTertiary
                                font.pixelSize: 10
                                font.family: "JetBrains Mono, monospace"
                            }
                        }
                    }

                    RowLayout {
                        Layout.alignment: Qt.AlignHCenter
                        spacing: 16

                        Rectangle {
                            width: 40
                            height: 40
                            radius: 20
                            color: prevMouseArea.containsMouse ? theme.backgroundTertiary : "transparent"
                            border.color: theme.border
                            border.width: 1

                            Text {
                                anchors.centerIn: parent
                                text: "󰒮"
                                color: activePlayer && activePlayer.canGoPrevious ? theme.textPrimary : theme.textTertiary
                                font.pixelSize: 16
                                font.family: "JetBrains Mono Nerd Font, monospace"
                            }

                            MouseArea {
                                id: prevMouseArea
                                anchors.fill: parent
                                hoverEnabled: true
                                enabled: activePlayer && activePlayer.canGoPrevious
                                onClicked: {
                                    console.log("Previous button clicked, activePlayer:", activePlayer ? activePlayer.identity : "null")
                                    console.log("canGoPrevious:", activePlayer ? activePlayer.canGoPrevious : "null")
                                    if (activePlayer && activePlayer.canGoPrevious) {
                                        console.log("Calling activePlayer.previous()")
                                        activePlayer.previous()
                                    } else {
                                        console.log("Previous not available or no active player")
                                    }
                                }
                            }

                            Behavior on color {
                                ColorAnimation { 
                                    duration: theme.fastAnimationDuration
                                    easing.type: Easing.OutCubic 
                                }
                            }
                        }

                        Rectangle {
                            width: 50
                            height: 50
                            radius: 25
                            color: playMouseArea.containsMouse ? theme.accent : theme.backgroundTertiary
                            border.color: theme.accent
                            border.width: 2

                            Text {
                                anchors.centerIn: parent
                                text: {
                                    if (!activePlayer) return "󰐊"
                                    return activePlayer.playbackState === MprisPlaybackState.Playing ? "󰏤" : "󰐊"
                                }
                                color: playMouseArea.containsMouse ? theme.backgroundPrimary : theme.textPrimary
                                font.pixelSize: 20
                                font.family: "JetBrains Mono Nerd Font, monospace"
                            }

                            MouseArea {
                                id: playMouseArea
                                anchors.fill: parent
                                hoverEnabled: true
                                onClicked: {
                                    console.log("Play/pause button clicked, activePlayer:", activePlayer ? activePlayer.identity : "null")
                                    if (activePlayer) {
                                        console.log("Current playback state:", activePlayer.playbackState)
                                        console.log("MprisPlaybackState.Playing:", MprisPlaybackState.Playing)
                                        console.log("MprisPlaybackState.Paused:", MprisPlaybackState.Paused)
                                        
                                        // Use togglePlaying if available, otherwise use play/pause
                                        if (activePlayer.canTogglePlaying) {
                                            console.log("Using togglePlaying()")
                                            try {
                                                activePlayer.togglePlaying()
                                                console.log("togglePlaying() called successfully")
                                            } catch (e) {
                                                console.log("togglePlaying() failed:", e)
                                            }
                                        } else {
                                            // Fallback to manual play/pause
                                            if (activePlayer.playbackState === MprisPlaybackState.Playing && activePlayer.canPause) {
                                                console.log("Using pause()")
                                                activePlayer.pause()
                                            } else if (activePlayer.playbackState !== MprisPlaybackState.Playing && activePlayer.canPlay) {
                                                console.log("Using play()")
                                                activePlayer.play()
                                            }
                                        }
                                    }
                                }
                            }

                            Behavior on color {
                                ColorAnimation { 
                                    duration: theme.fastAnimationDuration
                                    easing.type: Easing.OutCubic 
                                }
                            }
                        }

                        Rectangle {
                            width: 40
                            height: 40
                            radius: 20
                            color: nextMouseArea.containsMouse ? theme.backgroundTertiary : "transparent"
                            border.color: theme.border
                            border.width: 1

                            Text {
                                anchors.centerIn: parent
                                text: "󰒭"
                                color: activePlayer && activePlayer.canGoNext ? theme.textPrimary : theme.textTertiary
                                font.pixelSize: 16
                                font.family: "JetBrains Mono Nerd Font, monospace"
                            }

                            MouseArea {
                                id: nextMouseArea
                                anchors.fill: parent
                                hoverEnabled: true
                                enabled: activePlayer && activePlayer.canGoNext
                                onClicked: {
                                    console.log("Next button clicked, activePlayer:", activePlayer ? activePlayer.identity : "null")
                                    if (activePlayer) {
                                        console.log("Calling activePlayer.next()")
                                        activePlayer.next()
                                    }
                                }
                            }

                            Behavior on color {
                                ColorAnimation { 
                                    duration: theme.fastAnimationDuration
                                    easing.type: Easing.OutCubic 
                                }
                            }
                        }
                    }
                }
            }
        }

        // Remove this MouseArea as it might interfere with controls
    }

    onOverlayVisibleChanged: {
        console.log("Overlay visibility changed to:", overlayVisible)
        if (overlayVisible) {
            overlayShown()
        } else {
            overlayHidden()
        }
    }

    function showOverlay() {
        console.log("showOverlay() called")
        console.log("  - Current overlayVisible:", overlayVisible)
        console.log("  - activePlayer:", activePlayer ? activePlayer.identity : "null")
        
        overlayVisible = true
        console.log("  - After setting: overlayVisible =", overlayVisible)
    }

    function hideOverlay() {
        console.log("hideOverlay() called")
        console.log("  - Current overlayVisible:", overlayVisible)
        
        overlayVisible = false
        console.log("  - After setting: overlayVisible =", overlayVisible)
    }

    function toggleOverlay() {
        console.log("toggleOverlay() called")
        console.log("  - Current state:", overlayVisible)
        console.log("  - activePlayer:", activePlayer ? activePlayer.identity : "null")
        console.log("  - Window visible:", visible)
        
        if (overlayVisible) {
            hideOverlay()
        } else {
            showOverlay()
        }
    }

    // Click outside to close - handled by auto-hide timer instead
}