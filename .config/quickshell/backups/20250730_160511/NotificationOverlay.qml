import QtQuick
import QtQuick.Layouts
import Quickshell
import Quickshell.Wayland
import "../"

PanelWindow {
    id: notificationOverlay
    
    property var screen: null
    property var barWindow: null
    property bool overlayVisible: false
    property var notificationWidget: null
    
    signal overlayHidden()
    
    visible: overlayVisible
    
    anchors {
        right: true
        top: true
    }
    
    margins {
        right: 10
        top: 40
    }
    
    width: 400
    height: Math.min(600, Math.max(200, notificationList.contentHeight + 40))
    
    WlrLayershell.layer: WlrLayer.Overlay
    WlrLayershell.exclusiveZone: 0
    WlrLayershell.keyboardFocus: WlrKeyboardFocus.OnDemand
    
    Theme {
        id: theme
    }
    
    // Background with blur effect
    Rectangle {
        anchors.fill: parent
        radius: 12
        color: theme.background
        border.width: 1
        border.color: theme.border
        opacity: 0.95
        
        // Header
        Rectangle {
            id: header
            anchors.top: parent.top
            anchors.left: parent.left
            anchors.right: parent.right
            height: 40
            radius: 12
            color: theme.backgroundSecondary
            
            Rectangle {
                anchors.bottom: parent.bottom
                anchors.left: parent.left
                anchors.right: parent.right
                height: 12
                color: theme.backgroundSecondary
            }
            
            RowLayout {
                anchors.fill: parent
                anchors.margins: 12
                
                Text {
                    text: "Notifications"
                    color: theme.textPrimary
                    font.pixelSize: 14
                    font.weight: Font.Bold
                    Layout.fillWidth: true
                }
                
                Text {
                    text: notificationWidget ? notificationWidget.notificationCount.toString() : "0"
                    color: theme.textSecondary
                    font.pixelSize: 12
                    font.family: "JetBrains Mono, monospace"
                }
                
                Rectangle {
                    width: 60
                    height: 20
                    radius: 10
                    color: theme.accent
                    
                    Text {
                        anchors.centerIn: parent
                        text: "Clear"
                        color: theme.background
                        font.pixelSize: 10
                        font.weight: Font.Bold
                    }
                    
                    MouseArea {
                        anchors.fill: parent
                        onClicked: {
                            if (notificationWidget) {
                                notificationWidget.clearAllNotifications()
                            }
                            hideOverlay()
                        }
                    }
                }
            }
        }
        
        // Notification list
        ListView {
            id: notificationList
            anchors.top: header.bottom
            anchors.left: parent.left
            anchors.right: parent.right
            anchors.bottom: parent.bottom
            anchors.margins: 12
            anchors.topMargin: 8

            model: notificationWidget ? notificationWidget.activeNotifications : []
            spacing: 8
            clip: true

            delegate: Rectangle {
                width: notificationList.width
                height: Math.max(80, contentColumn.implicitHeight + 16)
                radius: 8
                color: theme.backgroundSecondary
                border.width: 1
                border.color: theme.border

                Column {
                    id: contentColumn
                    anchors.fill: parent
                    anchors.margins: 12
                    spacing: 6

                    Row {
                        width: parent.width
                        spacing: 8

                        Text {
                            text: modelData.summary || ""
                            color: theme.textPrimary
                            font.pixelSize: 13
                            font.weight: Font.Bold
                            wrapMode: Text.WordWrap
                            width: parent.width - timeText.width - 8
                            maximumLineCount: 2
                            elide: Text.ElideRight
                        }

                        Text {
                            id: timeText
                            text: formatTime(modelData.timestamp)
                            color: theme.textTertiary
                            font.pixelSize: 10
                            anchors.verticalCenter: parent.verticalCenter
                        }
                    }

                    Text {
                        text: modelData.body || ""
                        color: theme.textSecondary
                        font.pixelSize: 11
                        wrapMode: Text.WordWrap
                        width: parent.width
                        maximumLineCount: 3
                        elide: Text.ElideRight
                    }

                    Text {
                        text: modelData.app_name || ""
                        color: theme.textTertiary
                        font.pixelSize: 10
                        font.italic: true
                    }
                }

                MouseArea {
                    anchors.fill: parent
                    onClicked: {
                        // Remove this specific notification
                        if (notificationWidget) {
                            notificationWidget.removeNotificationFromList(modelData.id)
                        }
                    }
                }
            }

            // Empty state
            Text {
                anchors.centerIn: parent
                text: "No notifications"
                color: theme.textSecondary
                font.pixelSize: 14
                visible: notificationList.count === 0
            }
        }
    }
    
    // Click outside to hide
    MouseArea {
        anchors.fill: parent
        z: -1
        onClicked: {
            hideOverlay()
        }
    }
    
    function showOverlay() {
        console.log("NotificationOverlay: Showing overlay")
        overlayVisible = true
    }
    
    function hideOverlay() {
        console.log("NotificationOverlay: Hiding overlay")
        overlayVisible = false
        overlayHidden()
    }
    
    function formatTime(timestamp) {
        const now = Date.now()
        const diff = now - timestamp
        const minutes = Math.floor(diff / 60000)
        const hours = Math.floor(minutes / 60)
        const days = Math.floor(hours / 24)
        
        if (days > 0) return days + "d"
        if (hours > 0) return hours + "h"
        if (minutes > 0) return minutes + "m"
        return "now"
    }
    
    Component.onCompleted: {
        console.log("NotificationOverlay: Component loaded")
    }
}
