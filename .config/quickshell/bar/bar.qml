import QtQuick
import QtQuick.Layouts
import Quickshell
import Quickshell.Wayland
import Quickshell.Services.SystemTray
import "./modules"
import "./modules/visualizerModules"
import "./modules/notificationModules"
import "./modules/calendarModules"
import "./modules/mprisModules"

PanelWindow {
    id: window

    property var targetScreen: Quickshell.screens[0]
    property int screenIndex: 0
    property bool showMprisOverlay: false
    property bool showCalendarOverlay: false

    property var timeModuleItem: null
    property var notificationModuleItem: null
    property bool showNotificationOverlay: false

    screen: targetScreen

    Theme {
        id: theme
    }

    property var workspaceRanges: [
      [1, 9]
    ]

    property var screenConfigs: [
        {
            left: ["workspaces"],
            center: ["time", "mpris"],
            right: ["network", "volume", "system", "notification"]
        }
    ]

    property var availableModules: {
        "workspaces": "hyprlandWorkspaces",
        "time": "calendarModules/TimeWidget",
        "system": "systemIndicators",
        "mpris": "mprisModules/MprisWidget",
        "volume": "volumeWidget",
        "network": "networkWidget",
        "notification": "notificationModules/notificationWidget",
    }

    property var currentConfig: getScreenConfig(screenIndex)

    anchors {
        left: true
        right: true
        top: true
        bottom: false
    }

    implicitHeight: 30
    visible: true
    color: "transparent"

    WlrLayershell.layer: WlrLayer.Top
    WlrLayershell.exclusiveZone: 30
    WlrLayershell.keyboardFocus: WlrKeyboardFocus.None
    WlrLayershell.namespace: "quickshell-bar"

    Component.onCompleted: {
        console.log("Bar: Static brackets bar initialized for screen", screenIndex)
    }

    Rectangle {
        id: bar
        anchors.fill: parent
        radius: 0
        color: theme.background

        RowLayout {
            anchors.fill: parent
            anchors.leftMargin: 2
            anchors.rightMargin: 2
            anchors.topMargin: 5
            anchors.bottomMargin: 6
            spacing: 4

            Row {
                Layout.alignment: Qt.AlignLeft
                spacing: 10

                Repeater {
                    model: currentConfig.left

                    Loader {
                        source: getModuleSource(modelData)
                        onLoaded: {
                            if (modelData === "workspaces" && item) {
                                item.screenIndex = window.screenIndex
                            }
                        }
                    }
                }
            }

            Item {
                Layout.fillWidth: true
                Layout.fillHeight: true
                Layout.alignment: Qt.AlignHCenter

                Row {
                    anchors.centerIn: parent
                    spacing: 0

                    Text {
                        text: "【"
                        color: theme.accent
                        font.pixelSize: 14
                        font.family: "JetBrains Mono, monospace"
                        anchors.verticalCenter: parent.verticalCenter
                    }

                    Row {
                        id: centerContent
                        spacing: 10
                        anchors.verticalCenter: parent.verticalCenter

                        Repeater {
                            model: currentConfig.center

                            Loader {
                                source: getModuleSource(modelData)
                                onLoaded: {
                                    if (modelData === "workspaces" && item) {
                                        item.screenIndex = window.screenIndex
                                    }
                                    if (modelData === "mpris" && item) {
                                        item.showMprisOverlayRequested.connect(function() {
                                            window.showMprisOverlay = !window.showMprisOverlay
                                        })

                                        item.hideMprisOverlayRequested.connect(function() {
                                            window.showMprisOverlay = false
                                        })
                                    }
                                    if (modelData === "time" && item) {
                                        window.timeModuleItem = item
                                        item.showCalendarOverlayRequested.connect(function() {
                                            window.showCalendarOverlay = !window.showCalendarOverlay
                                        })

                                        item.hideCalendarOverlayRequested.connect(function() {
                                            window.showCalendarOverlay = false
                                        })
                                    }

                                }
                            }
                        }
                    }

                    Text {
                        text: "】"
                        color: theme.accent
                        font.pixelSize: 14
                        font.family: "JetBrains Mono, monospace"
                        anchors.verticalCenter: parent.verticalCenter
                    }
                }
            }

            Row {
                Layout.alignment: Qt.AlignRight
                spacing: 0
                visible: window.screenIndex === 0

                Text {
                    text: "【"
                    color: theme.accent
                    font.pixelSize: 14
                    font.family: "JetBrains Mono, monospace"
                    anchors.verticalCenter: parent.verticalCenter
                }

                VisualizerWidget {
                    id: visualizer
                    anchors.verticalCenter: parent.verticalCenter
                }

                Text {
                    text: "】"
                    color: theme.accent
                    font.pixelSize: 14
                    font.family: "JetBrains Mono, monospace"
                    anchors.verticalCenter: parent.verticalCenter
                }
            }

            Row {
                Layout.alignment: Qt.AlignRight
                spacing: 0
                visible: SystemTray.items && SystemTray.items.values && SystemTray.items.values.length > 0

                Text {
                    text: "【"
                    color: theme.accent
                    font.pixelSize: 14
                    font.family: "JetBrains Mono, monospace"
                    anchors.verticalCenter: parent.verticalCenter
                }

                SystemTrayWidget {
                    id: systemTray
                    anchors.verticalCenter: parent.verticalCenter
                }

                Text {
                    text: "】"
                    color: theme.accent
                    font.pixelSize: 14
                    font.family: "JetBrains Mono, monospace"
                    anchors.verticalCenter: parent.verticalCenter
                }
            }

            Row {
                Layout.alignment: Qt.AlignRight
                spacing: 0

                Text {
                    text: "【"
                    color: theme.accent
                    font.pixelSize: 14
                    font.family: "JetBrains Mono, monospace"
                    anchors.verticalCenter: parent.verticalCenter
                }

                Row {
                    id: rightContent
                    spacing: 0
                    anchors.verticalCenter: parent.verticalCenter

                    Repeater {
                        model: currentConfig.right

                        Loader {
                            source: getModuleSource(modelData)
                            onLoaded: {
                                if (modelData === "workspaces" && item) {
                                    item.screenIndex = window.screenIndex
                                }
                                if (modelData === "notification" && item) {
                                    window.notificationModuleItem = item


                                    if (item.hasOwnProperty("showNotificationOverlayRequested")) {
                                        item.showNotificationOverlayRequested.connect(function() {
                                            window.showNotificationOverlay = !window.showNotificationOverlay
                                        })
                                    }

                                    if (item.hasOwnProperty("hideNotificationOverlayRequested")) {
                                        item.hideNotificationOverlayRequested.connect(function() {
                                            window.showNotificationOverlay = false
                                        })
                                    }
                                }
                            }
                        }
                    }
                }

                Text {
                    text: "】"
                    color: theme.accent
                    font.pixelSize: 14
                    font.family: "JetBrains Mono, monospace"
                    anchors.verticalCenter: parent.verticalCenter
                }
            }
        }
    }

    MprisOverlay {
        id: mprisOverlay
        screen: window.targetScreen
        barWindow: window
        overlayVisible: window.showMprisOverlay

        onOverlayHidden: {
            window.showMprisOverlay = false
        }
    }

    CalendarOverlay {
        id: calendarOverlay
        screen: window.targetScreen
        barWindow: window
        overlayVisible: window.showCalendarOverlay

        onOverlayHidden: {
            window.showCalendarOverlay = false
        }
    }

    NotificationOverlay {
        id: notificationOverlay
        screen: window.targetScreen
        barWindow: window
        overlayVisible: window.showNotificationOverlay
        notificationWidget: window.notificationModuleItem

        onOverlayHidden: {
            window.showNotificationOverlay = false
        }
    }





    function getScreenConfig(screenIndex) {
        if (screenIndex < screenConfigs.length) {
            return screenConfigs[screenIndex]
        }
        return {
            left: ["workspaces"],
            center: ["time", "mpris"],
            right: ["system"]
        }
    }

    function getWorkspaceRange(screenIndex) {
        if (screenIndex < workspaceRanges.length) {
            return workspaceRanges[screenIndex]
        }
        return [1, 10]
    }

    function getModuleSource(moduleName) {
        var moduleFileMap = {
            "workspaces": "hyprlandWorkspaces",
            "time": "calendarModules/TimeWidget",
            "system": "systemIndicators",
            "mpris": "mprisModules/MprisWidget",
            "volume": "volumeWidget",
            "network": "networkWidget",
            "notification": "notificationModules/notificationWidget"
        }
        var fileName = moduleFileMap[moduleName]
        if (fileName) {
            return "./modules/" + fileName + ".qml"
        }
        return ""
    }
}