import QtQuick
import "../modules"

Rectangle {
    id: timeModule
    objectName: "timeModule"
    width: timeText.width + 20
    height: 18
    radius: 9
    color: mouseArea.containsMouse ? Qt.rgba(theme.accent.r, theme.accent.g, theme.accent.b, 0.2) : "transparent"

    property date currentDate: new Date()

    signal showCalendarOverlayRequested()
    signal hideCalendarOverlayRequested()

    Theme {
        id: theme
    }



    Text {
        id: timeText
        anchors.centerIn: parent
        text: Qt.formatDateTime(currentDate, "h:mm AP")
        color: theme.textPrimary
        font.pixelSize: 12
        font.family: "JetBrains Mono, monospace"
        font.weight: Font.Medium
    }

    MouseArea {
        id: mouseArea
        anchors.fill: parent
        hoverEnabled: true
        acceptedButtons: Qt.LeftButton | Qt.RightButton

        onClicked: function(mouse) {
            if (mouse.button === Qt.LeftButton) {
                showCalendarOverlayRequested()
            }
        }
    }

    Behavior on color {
        ColorAnimation { duration: 200 }
    }

    Timer {
        interval: 1000
        running: true
        repeat: true
        onTriggered: {
            currentDate = new Date()
        }
    }
}
