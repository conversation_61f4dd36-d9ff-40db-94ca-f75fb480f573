import QtQuick
import QtQuick.Layouts
import Quickshell
import Quickshell.Wayland
import Quickshell.Io
import "../"

PanelWindow {
    id: calendarOverlay

    property var screen
    property var barWindow
    property bool overlayVisible: false
    property int overlayWidth: 600
    property int overlayHeight: 300

    signal overlayHidden
    signal overlayShown

    screen: Quickshell.screens[0]
    visible: overlayVisible
    color: "transparent"
    implicitWidth: overlayWidth
    implicitHeight: overlayHeight
    focusable: true

    anchors {
        top: true
        left: false
        right: false
        bottom: false
    }

    margins {
        top: 8
        left: 4
        right: 4
        bottom: 0
    }

    WlrLayershell.layer: WlrLayer.Top
    WlrLayershell.exclusiveZone: 0
    WlrLayershell.keyboardFocus: WlrKeyboardFocus.OnDemand

    property var debugHelper: null

    function showOverlay() {
        try {
            if (debugHelper)
                debugHelper.logEnhancedCalendar("showOverlay() called");
            overlayVisible = true;
        } catch (error) {
            console.error("Error showing calendar overlay:", error);
            overlayVisible = true;
        }
    }

    function hideOverlay() {
        try {
            if (debugHelper)
                debugHelper.logEnhancedCalendar("hideOverlay() called");
            overlayHidden();
        } catch (error) {
            console.error("Error hiding calendar overlay:", error);
            overlayHidden();
        }
    }

    function updatePosition() {
        if (barWindow)
        // Remove the problematic x/y setting that causes the error
        // The anchors and margins should handle positioning
        {}
    }

    Theme {
        id: theme
    }

    // Process for calling the Python calendar manager
    Process {
        id: calendarProcess
        running: false

        onExited: function (exitCode, exitStatus) {
            if (debugHelper) {
                debugHelper.logEnhancedCalendar(`Calendar process exited with code: ${exitCode}`);
            }
            console.log("Calendar process exited with code:", exitCode);
        }

        onStarted: {
            if (debugHelper) {
                debugHelper.logEnhancedCalendar("Calendar process started");
            }
            console.log("Calendar process started");
        }
    }

    function openDateFile(date) {
        try {
            var dateString = Qt.formatDate(date, "yyyy-MM-dd");
            var scriptPath = Qt.resolvedUrl("calendarManager.py").toString().replace("file://", "");

            if (debugHelper) {
                debugHelper.logEnhancedCalendar(`Opening date file for: ${dateString}`);
            }
            console.log("Opening date file for:", dateString);

            calendarProcess.command = ["python3", scriptPath, dateString];
            calendarProcess.running = true;
        } catch (error) {
            console.error("Error opening date file:", error);
        }
    }

    Item {
        id: overlayContainer
        anchors.fill: parent

        Rectangle {
            id: backgroundRect
            anchors.fill: parent
            color: theme.background
            radius: theme.largeBorderRadius
            opacity: overlayVisible ? 1.0 : 0.0

            Behavior on opacity {
                NumberAnimation {
                    duration: theme.slowAnimationDuration
                    easing.type: Easing.OutCubic
                }
            }

            RowLayout {
                id: mainLayout
                anchors.fill: parent
                anchors.margins: 16
                anchors.topMargin: 24
                spacing: 16

                Item {
                    id: calendarContainer
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    Layout.preferredWidth: overlayWidth * 0.7

                    // Inline EnhancedCalendarWidget
                    Rectangle {
                        id: calendarWidget
                        anchors.fill: parent
                        color: "transparent"

                        property date currentDate: new Date()
                        property date selectedDate: new Date()
                        property int currentMonth: currentDate.getMonth()
                        property int currentYear: currentDate.getFullYear()
                        property string transitionDirection: "none"

                        signal dateSelected(date selectedDate)
                        signal monthChanged(int month, int year)

                        function getDaysInMonth(month, year) {
                            return new Date(year, month + 1, 0).getDate();
                        }

                        function getFirstDayOfMonth(month, year) {
                            return new Date(year, month, 1).getDay();
                        }

                        function isToday(day, month, year) {
                            var today = new Date();
                            return day === today.getDate() && month === today.getMonth() && year === today.getFullYear();
                        }

                        function isSelected(day, month, year) {
                            return day === selectedDate.getDate() && month === selectedDate.getMonth() && year === selectedDate.getFullYear();
                        }

                        function navigateToMonth(month, year, direction) {
                            transitionDirection = direction;
                            currentMonth = month;
                            currentYear = year;
                            monthChanged(currentMonth, currentYear);
                            transitionAnimation.start();
                        }

                        function navigateToPreviousMonth() {
                            var prevMonth = currentMonth - 1;
                            var prevYear = currentYear;
                            if (prevMonth < 0) {
                                prevMonth = 11;
                                prevYear--;
                            }
                            navigateToMonth(prevMonth, prevYear, "left");
                        }

                        function navigateToNextMonth() {
                            var nextMonth = currentMonth + 1;
                            var nextYear = currentYear;
                            if (nextMonth > 11) {
                                nextMonth = 0;
                                nextYear++;
                            }
                            navigateToMonth(nextMonth, nextYear, "right");
                        }

                        function getDayInfo(index) {
                            var firstDay = getFirstDayOfMonth(currentMonth, currentYear);
                            var daysInMonth = getDaysInMonth(currentMonth, currentYear);
                            var daysInPrevMonth = getDaysInMonth(currentMonth - 1 < 0 ? 11 : currentMonth - 1, currentMonth - 1 < 0 ? currentYear - 1 : currentYear);

                            var dayNumber;
                            var month = currentMonth;
                            var year = currentYear;

                            if (index < firstDay) {
                                dayNumber = daysInPrevMonth - (firstDay - index - 1);
                                month = currentMonth - 1;
                                if (month < 0) {
                                    month = 11;
                                    year = currentYear - 1;
                                }
                            } else if (index >= firstDay + daysInMonth) {
                                dayNumber = index - firstDay - daysInMonth + 1;
                                month = currentMonth + 1;
                                if (month > 11) {
                                    month = 0;
                                    year = currentYear + 1;
                                }
                            } else {
                                dayNumber = index - firstDay + 1;
                            }

                            return {
                                day: dayNumber,
                                month: month,
                                year: year
                            };
                        }

                        ColumnLayout {
                            anchors.fill: parent
                            spacing: 8

                            // Header with navigation
                            RowLayout {
                                Layout.fillWidth: true
                                Layout.preferredHeight: 32
                                spacing: 12

                                Rectangle {
                                    width: 24
                                    height: 24
                                    color: prevMouseArea.containsMouse ? theme.pink : "transparent"
                                    radius: theme.smallBorderRadius

                                    Text {
                                        anchors.centerIn: parent
                                        text: "‹"
                                        color: prevMouseArea.containsMouse ? theme.black : theme.textSecondary
                                        font.pixelSize: 16
                                        font.family: "JetBrains Mono, monospace"
                                        font.weight: Font.Bold
                                    }

                                    MouseArea {
                                        id: prevMouseArea
                                        anchors.fill: parent
                                        hoverEnabled: true
                                        onClicked: calendarWidget.navigateToPreviousMonth()
                                    }

                                    Behavior on color {
                                        ColorAnimation {
                                            duration: theme.fastAnimationDuration
                                        }
                                    }
                                }

                                Text {
                                    Layout.fillWidth: true
                                    text: Qt.formatDate(new Date(calendarWidget.currentYear, calendarWidget.currentMonth, 1), "MMMM yyyy")
                                    color: theme.textPrimary
                                    font.pixelSize: 14
                                    font.family: "JetBrains Mono, monospace"
                                    font.weight: Font.Bold
                                    horizontalAlignment: Text.AlignHCenter
                                }

                                Rectangle {
                                    width: 24
                                    height: 24
                                    color: nextMouseArea.containsMouse ? theme.pink : "transparent"
                                    radius: theme.smallBorderRadius

                                    Text {
                                        anchors.centerIn: parent
                                        text: "›"
                                        color: nextMouseArea.containsMouse ? theme.black : theme.textSecondary
                                        font.pixelSize: 16
                                        font.family: "JetBrains Mono, monospace"
                                        font.weight: Font.Bold
                                    }

                                    MouseArea {
                                        id: nextMouseArea
                                        anchors.fill: parent
                                        hoverEnabled: true
                                        onClicked: calendarWidget.navigateToNextMonth()
                                    }

                                    Behavior on color {
                                        ColorAnimation {
                                            duration: theme.fastAnimationDuration
                                        }
                                    }
                                }
                            }

                            // Days of week header
                            GridLayout {
                                Layout.fillWidth: true
                                Layout.preferredHeight: 20
                                columns: 7
                                rowSpacing: 0
                                columnSpacing: 0

                                Repeater {
                                    model: ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"]
                                    Text {
                                        Layout.fillWidth: true
                                        Layout.preferredHeight: 20
                                        text: modelData
                                        color: theme.textSecondary
                                        font.pixelSize: 10
                                        font.family: "JetBrains Mono, monospace"
                                        font.weight: Font.Bold
                                        horizontalAlignment: Text.AlignHCenter
                                        verticalAlignment: Text.AlignVCenter
                                    }
                                }
                            }

                            // Calendar grid container (inline MonthTransitionContainer)
                            Item {
                                Layout.fillWidth: true
                                Layout.fillHeight: true

                                property real animationProgress: 0

                                GridLayout {
                                    anchors.fill: parent
                                    columns: 7
                                    rowSpacing: 2
                                    columnSpacing: 2

                                    Repeater {
                                        model: 42

                                        Rectangle {
                                            Layout.fillWidth: true
                                            Layout.fillHeight: true
                                            Layout.minimumHeight: 24

                                            property var dayInfo: calendarWidget.getDayInfo(index)
                                            property bool isCurrentMonth: dayInfo ? dayInfo.month === calendarWidget.currentMonth : false
                                            property bool isTodayDay: dayInfo ? calendarWidget.isToday(dayInfo.day, dayInfo.month, dayInfo.year) : false
                                            property bool isSelectedDay: dayInfo ? calendarWidget.isSelected(dayInfo.day, dayInfo.month, dayInfo.year) : false
                                            property bool isHovered: false

                                            color: {
                                                if (!dayInfo)
                                                    return "transparent";
                                                if (isSelectedDay)
                                                    return theme.pink;
                                                if (isTodayDay)
                                                    return Qt.rgba(theme.accent.r, theme.accent.g, theme.accent.b, 0.3);
                                                if (isHovered && isCurrentMonth)
                                                    return Qt.rgba(theme.textPrimary.r, theme.textPrimary.g, theme.textPrimary.b, 0.1);
                                                return "transparent";
                                            }

                                            radius: theme.smallBorderRadius

                                            Text {
                                                anchors.centerIn: parent
                                                text: dayInfo ? dayInfo.day : ""
                                                color: {
                                                    if (!dayInfo)
                                                        return "transparent";
                                                    if (isSelectedDay)
                                                        return theme.black;
                                                    if (!isCurrentMonth)
                                                        return theme.textSecondary;
                                                    if (isTodayDay)
                                                        return theme.accent;
                                                    return theme.textPrimary;
                                                }
                                                font.pixelSize: 10
                                                font.family: "JetBrains Mono, monospace"
                                                font.weight: parent.isTodayDay ? Font.Bold : Font.Normal
                                            }

                                            MouseArea {
                                                anchors.fill: parent
                                                hoverEnabled: true

                                                onEntered: {
                                                    if (parent.dayInfo && parent.isCurrentMonth) {
                                                        parent.isHovered = true;
                                                    }
                                                }

                                                onExited: {
                                                    parent.isHovered = false;
                                                }

                                                onClicked: {
                                                    if (parent.dayInfo) {
                                                        var dayInfo = parent.dayInfo;
                                                        var newDate = new Date(dayInfo.year, dayInfo.month, dayInfo.day);
                                                        calendarWidget.selectedDate = newDate;
                                                        calendarWidget.dateSelected(newDate);

                                                        // Open the date file using Python script
                                                        openDateFile(newDate);
                                                    }
                                                }
                                            }

                                            Behavior on color {
                                                ColorAnimation {
                                                    duration: theme.fastAnimationDuration
                                                }
                                            }
                                        }
                                    }
                                }

                                NumberAnimation {
                                    id: transitionAnimation
                                    property: "animationProgress"
                                    from: 0
                                    to: 1
                                    duration: theme.animationDuration
                                    easing.type: Easing.OutCubic
                                    onFinished: {
                                        calendarWidget.transitionDirection = "none";
                                    }
                                }
                            }
                        }

                        Timer {
                            interval: 60000
                            running: true
                            repeat: true
                            triggeredOnStart: false
                            onTriggered: {
                                currentDate = new Date();
                            }
                        }
                    }
                }

                Item {
                    id: timerContainer
                    Layout.fillHeight: true
                    Layout.preferredWidth: overlayWidth * 0.3

                    TimerWidget {
                        id: timerWidget
                        anchors.fill: parent
                        debugHelper: calendarOverlay.debugHelper
                    }
                }
            }

            // Mouse area for handling clicks - allows content interaction
            MouseArea {
                anchors.fill: parent
                propagateComposedEvents: true
                acceptedButtons: Qt.LeftButton | Qt.RightButton

                onClicked: function (mouse) {
                    // Check if click is outside the main content area
                    var clickX = mouse.x;
                    var clickY = mouse.y;
                    var contentLeft = mainLayout.x;
                    var contentTop = mainLayout.y;
                    var contentRight = mainLayout.x + mainLayout.width;
                    var contentBottom = mainLayout.y + mainLayout.height;

                    if (clickX < contentLeft || clickX > contentRight || clickY < contentTop || clickY > contentBottom) {
                        // Click outside content - hide overlay
                        try {
                            if (debugHelper)
                                debugHelper.logEnhancedCalendar("Click outside content, hiding overlay");
                            hideOverlay();
                            mouse.accepted = true;
                        } catch (error) {
                            console.error("Error hiding calendar overlay:", error);
                            overlayHidden();
                            mouse.accepted = true;
                        }
                    } else {
                        // Click inside content - let content handle it
                        mouse.accepted = false;
                    }
                }
            }
        }
    }

    onOverlayVisibleChanged: {
        try {
            if (overlayVisible) {
                if (debugHelper)
                    debugHelper.logEnhancedCalendar("Overlay showing");
                updatePosition();
                // Give the timer widget focus when overlay is shown
                Qt.callLater(function () {
                    try {
                        if (timerWidget) {
                            timerWidget.forceActiveFocus();
                        }
                    } catch (focusError) {
                        console.error("Error setting timer widget focus:", focusError);
                    }
                });
                overlayShown();
            } else {
                if (debugHelper)
                    debugHelper.logEnhancedCalendar("Overlay hiding");
                overlayHidden();
            }
        } catch (error) {
            console.error("Error in overlay visibility change:", error);
            // Ensure the overlay state is consistent even if there's an error
            if (overlayVisible) {
                overlayShown();
            } else {
                overlayHidden();
            }
        }
    }
}
