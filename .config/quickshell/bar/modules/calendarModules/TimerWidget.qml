import QtQuick
import QtQuick.Layouts
import "../"

Rectangle {
    id: timerWidget
    color: "transparent"
    
    focus: true
    activeFocusOnTab: true
    
    Component.onCompleted: {
        forceActiveFocus()
    }

    property string timerState: "input"
    property int totalSeconds: 0
    property int remainingSeconds: 0
    property string inputDigits: "000000"

    signal timerStarted()
    signal timerCompleted()

    Theme {
        id: theme
    }

    property var debugHelper: null

    function startTimer() {
        if (totalSeconds > 0) {
            if (debugHelper) debugHelper.logTimer("Starting timer with " + totalSeconds + " seconds")
            timerState = "running"
            remainingSeconds = totalSeconds
            countdownTimer.start()
            timerStarted()
        }
    }

    function pauseTimer() {
        if (timerState === "running") {
            if (debugHelper) debugHelper.logTimer("Timer paused at " + remainingSeconds + " seconds")
            timerState = "paused"
            countdownTimer.stop()
        } else if (timerState === "paused") {
            if (debugHelper) debugHelper.logTimer("Timer resumed with " + remainingSeconds + " seconds")
            timerState = "running"
            countdownTimer.start()
        }
    }

    function resetTimer() {
        if (debugHelper) debugHelper.logTimer("Timer reset")
        timerState = "input"
        countdownTimer.stop()
        remainingSeconds = 0
        inputDigits = "000000"
        updateTotalSeconds()
    }

    function formatTime(totalSecs) {
        var h = Math.floor(totalSecs / 3600)
        var m = Math.floor((totalSecs % 3600) / 60)
        var s = totalSecs % 60
        return (h < 10 ? "0" : "") + h + ":" + 
               (m < 10 ? "0" : "") + m + ":" + 
               (s < 10 ? "0" : "") + s
    }

    function formatInputTime() {
        var h = inputDigits.substring(0, 2)
        var m = inputDigits.substring(2, 4)
        var s = inputDigits.substring(4, 6)
        return h + ":" + m + ":" + s
    }

    function addDigit(digit) {
        inputDigits = inputDigits.substring(1) + digit
        updateTotalSeconds()
    }

    function addQuickTime(seconds) {
        var currentTotal = parseInt(inputDigits.substring(0, 2)) * 3600 + 
                          parseInt(inputDigits.substring(2, 4)) * 60 + 
                          parseInt(inputDigits.substring(4, 6))
        currentTotal += seconds
        
        var h = Math.floor(currentTotal / 3600)
        var m = Math.floor((currentTotal % 3600) / 60)
        var s = currentTotal % 60
        
        h = Math.min(99, h)
        
        inputDigits = (h < 10 ? "0" : "") + h + 
                     (m < 10 ? "0" : "") + m + 
                     (s < 10 ? "0" : "") + s
        updateTotalSeconds()
    }

    function updateTotalSeconds() {
        var h = parseInt(inputDigits.substring(0, 2))
        var m = parseInt(inputDigits.substring(2, 4))
        var s = parseInt(inputDigits.substring(4, 6))
        totalSeconds = h * 3600 + m * 60 + s
    }

    Timer {
        id: countdownTimer
        interval: 1000
        repeat: true
        running: false
        onTriggered: {
            if (remainingSeconds > 0) {
                remainingSeconds--
                if (debugHelper) debugHelper.logTimer("Timer countdown: " + remainingSeconds + " seconds remaining")
            } else {
                timerState = "input"
                stop()
                timerCompleted()
                if (debugHelper) debugHelper.logTimer("Timer completed - returning to input state")
            }
        }
    }

    Keys.onPressed: function(event) {
        console.log("Timer key pressed:", event.key, "State:", timerState, "Focus:", activeFocus, "Text:", event.text)
        if (timerState === "input") {
            if (event.key >= Qt.Key_0 && event.key <= Qt.Key_9) {
                var digit = event.key - Qt.Key_0
                console.log("Adding digit:", digit)
                addDigit(digit.toString())
                event.accepted = true
            } else if (event.key === Qt.Key_Backspace || event.key === Qt.Key_Delete) {
                console.log("Backspace/Delete pressed - clearing timer")
                resetTimer()
                event.accepted = true
            } else if (event.key === Qt.Key_Return || event.key === Qt.Key_Enter) {
                console.log("Enter pressed - starting timer")
                if (totalSeconds > 0) {
                    startTimer()
                }
                event.accepted = true
            }
        }
    }

    ColumnLayout {
        anchors.fill: parent
        spacing: 12

        Text {
            text: "Timer"
            color: theme.textPrimary
            font.pixelSize: 16
            font.family: "JetBrains Mono, monospace"
            font.weight: Font.Bold
            Layout.alignment: Qt.AlignHCenter
        }

        Item {
            Layout.fillWidth: true
            Layout.fillHeight: true

            Item {
                anchors.centerIn: parent
                width: Math.min(parent.width, parent.height) - 20
                height: width
                visible: timerState !== "input"

                Canvas {
                    id: progressCanvas
                    anchors.fill: parent
                    visible: timerState === "running" || timerState === "paused"


                onPaint: {
                    if (totalSeconds === 0) return
                    
                    var ctx = getContext("2d")
                    ctx.clearRect(0, 0, width, height)
                    
                    var centerX = width / 2
                    var centerY = height / 2
                    var radius = Math.min(width, height) / 2 - 10
                    
                    var progress = remainingSeconds / totalSeconds
                    
                    var startAngle = -Math.PI / 2
                    
                    var endAngle = startAngle - (2 * Math.PI * progress)
                    
                    ctx.beginPath()
                    ctx.arc(centerX, centerY, radius, startAngle, endAngle, true) 
                    ctx.lineWidth = 6
                    ctx.lineCap = "round"
                    ctx.strokeStyle = theme.cyan
                    ctx.stroke()
                }
              }

                Text {
                    anchors.centerIn: parent
                    text: timerState === "input" ? "00:00:00" : formatTime(remainingSeconds)
                    color: theme.textPrimary
                    font.pixelSize: 14
                    font.family: "JetBrains Mono, monospace"
                    font.weight: Font.Bold
                }
            }

            ColumnLayout {
                anchors.centerIn: parent
                spacing: 12
                visible: timerState === "input"

                Rectangle {
                    Layout.alignment: Qt.AlignHCenter
                    width: timeDisplay.width + 20
                    height: timeDisplay.height + 10
                    color: "transparent"
                    border.color: "transparent"
                    border.width: 0
                    radius: theme.smallBorderRadius

                    Text {
                        id: timeDisplay
                        anchors.centerIn: parent
                        text: formatInputTime()
                        color: theme.textPrimary
                        font.pixelSize: 24
                        font.family: "JetBrains Mono, monospace"
                        font.weight: Font.Bold
                    }

                    MouseArea {
                        anchors.fill: parent
                        onClicked: {
                            timerWidget.forceActiveFocus()
                            console.log("Timer clicked - forcing focus, activeFocus:", timerWidget.activeFocus)
                        }
                    }
                }

                Rectangle {
                    width: parent.width * 0.8
                    height: 1
                    color: theme.textSecondary
                    Layout.alignment: Qt.AlignHCenter
                }

                RowLayout {
                    spacing: 12
                    Layout.alignment: Qt.AlignHCenter

                    Rectangle {
                        width: 60
                        height: 30
                        color: theme.backgroundSecondary
                        radius: theme.smallBorderRadius

                        Text {
                            anchors.centerIn: parent
                            text: "+0:30"
                            color: theme.textPrimary
                            font.pixelSize: 10
                            font.family: "JetBrains Mono, monospace"
                            font.weight: Font.Bold
                        }

                        MouseArea {
                            anchors.fill: parent
                            onClicked: addQuickTime(30)
                        }
                    }

                    Rectangle {
                        width: 60
                        height: 30
                        color: theme.backgroundSecondary
                        radius: theme.smallBorderRadius

                        Text {
                            anchors.centerIn: parent
                            text: "+1:00"
                            color: theme.textPrimary
                            font.pixelSize: 10
                            font.family: "JetBrains Mono, monospace"
                            font.weight: Font.Bold
                        }

                        MouseArea {
                            anchors.fill: parent
                            onClicked: addQuickTime(60)
                        }
                    }
                }

                Rectangle {
                    width: 80
                    height: 30
                    color: totalSeconds > 0 ? theme.cyan : theme.backgroundSecondary
                    radius: theme.smallBorderRadius
                    Layout.alignment: Qt.AlignHCenter

                    Text {
                        anchors.centerIn: parent
                        text: "START"
                        color: totalSeconds > 0 ? theme.black : theme.textSecondary
                        font.pixelSize: 12
                        font.family: "JetBrains Mono, monospace"
                        font.weight: Font.Bold
                    }

                    MouseArea {
                        anchors.fill: parent
                        enabled: totalSeconds > 0
                        onClicked: startTimer()
                    }
                }
            }

            RowLayout {
                anchors.bottom: parent.bottom
                anchors.horizontalCenter: parent.horizontalCenter
                spacing: 8
                visible: timerState === "running" || timerState === "paused"

                Rectangle {
                    width: 60
                    height: 25
                    color: theme.blue
                    radius: theme.smallBorderRadius

                    Text {
                        anchors.centerIn: parent
                        text: timerState === "running" ? "PAUSE" : "RESUME"
                        color: theme.black
                        font.pixelSize: 9
                        font.family: "JetBrains Mono, monospace"
                        font.weight: Font.Bold
                    }

                    MouseArea {
                        anchors.fill: parent
                        onClicked: pauseTimer()
                    }
                }

                Rectangle {
                    width: 60
                    height: 25
                    color: theme.magenta
                    radius: theme.smallBorderRadius

                    Text {
                        anchors.centerIn: parent
                        text: "RESET"
                        color: theme.black
                        font.pixelSize: 9
                        font.family: "JetBrains Mono, monospace"
                        font.weight: Font.Bold
                    }

                    MouseArea {
                        anchors.fill: parent
                        onClicked: resetTimer()
                    }
                }
            }
        }
    }

    onRemainingSecondsChanged: {
        if (timerState === "running") {
            progressCanvas.requestPaint()
        }
    }
}
