import QtQuick
import QtQuick.Layouts
import Quickshell.Io

Rectangle {
    id: networkWidget
    width: Math.min(expanded ? expandedWidth : collapsedWidth, maxWidth)
    height: 18
    radius: 9
    color: "transparent"
    clip: true

    property bool expanded: false
    property int collapsedWidth: 32
    property int expandedWidth: 120
    property int maxWidth: 120

    property string connectionType: "disconnected"
    property string networkIcon: getNetworkIcon()
    property real downloadSpeed: 0
    property real uploadSpeed: 0
    property bool isConnected: false
    property string activeInterface: ""

    // Network monitoring
    property var previousRxBytes: 0
    property var previousTxBytes: 0
    property var lastUpdateTime: 0

    function getNetworkIcon() {
        if (!isConnected || connectionType === "disconnected") {
            return "󰤭"  // Disconnected icon
        } else if (connectionType === "wifi") {
            return "󰤨"  // WiFi icon
        } else if (connectionType === "ethernet") {
            return "󰈀"  // Ethernet icon
        } else {
            return "󰤭"  // Default disconnected
        }
    }

    function updateNetworkIcon() {
        networkIcon = getNetworkIcon()
    }

    Theme {
        id: theme
    }

    Timer {
        id: collapseTimer
        interval: 3000
        onTriggered: expanded = false
    }

    // Network status monitoring timer
    Timer {
        id: networkTimer
        interval: 2000
        running: true
        repeat: true
        onTriggered: {
            checkNetworkStatus()
            updateBandwidth()
        }
    }

    // Process to check network interfaces
    Process {
        id: networkStatusProcess
        command: ["ip", "route", "get", "*******"]
        stdout: StdioCollector {
            id: networkStatusCollector
        }
        
        onExited: function(exitCode, exitStatus) {
            if (exitCode === 0) {
                var output = networkStatusCollector.text.trim()
                parseNetworkStatus(output)
            } else {
                connectionType = "disconnected"
                isConnected = false
                updateNetworkIcon()
            }
        }
    }

    // Process to get network statistics
    Process {
        id: networkStatsProcess
        command: ["cat", "/proc/net/dev"]
        stdout: StdioCollector {
            id: networkStatsCollector
        }
        
        onExited: function(exitCode, exitStatus) {
            if (exitCode === 0) {
                parseNetworkStats(networkStatsCollector.text)
            }
        }
    }

    Component.onCompleted: {
        checkNetworkStatus()
        updateBandwidth()
    }

    onExpandedChanged: {
        if (expanded) {
            collapseTimer.start()
        } else {
            collapseTimer.stop()
        }
    }

    function checkNetworkStatus() {
        networkStatusProcess.running = true
    }

    function updateBandwidth() {
        networkStatsProcess.running = true
    }

    function parseNetworkStatus(output) {
        if (output.includes("dev")) {
            var parts = output.split(" ")
            for (var i = 0; i < parts.length; i++) {
                if (parts[i] === "dev" && i + 1 < parts.length) {
                    activeInterface = parts[i + 1]
                    break
                }
            }
            
            // Determine connection type based on interface name
            if (activeInterface.startsWith("wl") || activeInterface.startsWith("wlan")) {
                connectionType = "wifi"
                isConnected = true
            } else if (activeInterface.startsWith("en") || activeInterface.startsWith("eth")) {
                connectionType = "ethernet"
                isConnected = true
            } else {
                connectionType = "disconnected"
                isConnected = false
            }
            updateNetworkIcon()
        } else {
            connectionType = "disconnected"
            isConnected = false
            updateNetworkIcon()
        }
    }

    function parseNetworkStats(output) {
        if (!isConnected || !activeInterface) return
        
        var lines = output.split('\n')
        var currentTime = Date.now()
        
        for (var i = 0; i < lines.length; i++) {
            var line = lines[i].trim()
            if (line.startsWith(activeInterface + ":")) {
                var parts = line.split(/\s+/)
                if (parts.length >= 10) {
                    var rxBytes = parseInt(parts[1])
                    var txBytes = parseInt(parts[9])
                    
                    if (previousRxBytes > 0 && lastUpdateTime > 0) {
                        var timeDiff = (currentTime - lastUpdateTime) / 1000
                        if (timeDiff > 0) {
                            downloadSpeed = (rxBytes - previousRxBytes) / timeDiff
                            uploadSpeed = (txBytes - previousTxBytes) / timeDiff
                        }
                    }
                    
                    previousRxBytes = rxBytes
                    previousTxBytes = txBytes
                    lastUpdateTime = currentTime
                }
                break
            }
        }
    }



    function formatBytes(bytes) {
        if (bytes < 1024) return bytes.toFixed(0) + " B/s"
        else if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + " KB/s"
        else return (bytes / (1024 * 1024)).toFixed(1) + " MB/s"
    }

    RowLayout {
        anchors.fill: parent
        anchors.leftMargin: 6
        anchors.rightMargin: 6
        spacing: 0

        Text {
            id: networkIconText
            text: networkWidget.networkIcon
            color: connectionType === "disconnected" ? theme.textSecondary : theme.accent
            font.pixelSize: 12
            font.family: "JetBrains Mono Nerd Font, monospace"
            Layout.alignment: Qt.AlignVCenter
        }

        Row {
            id: bandwidthContent
            visible: expanded && connectionType !== "disconnected"
            spacing: 2
            Layout.alignment: Qt.AlignVCenter

            Text {
                text: "󰇚"
                color: "#99ffdd"
                font.pixelSize: 8
                font.family: "JetBrains Mono Nerd Font, monospace"
            }

            Text {
                text: formatBytes(downloadSpeed)
                color: "#99ffdd"
                font.pixelSize: 8
                font.family: "JetBrains Mono, monospace"
            }

            Text {
                text: "󰕒"
                color: "#ffcc66"
                font.pixelSize: 8
                font.family: "JetBrains Mono Nerd Font, monospace"
            }

            Text {
                text: formatBytes(uploadSpeed)
                color: "#ffcc66"
                font.pixelSize: 8
                font.family: "JetBrains Mono, monospace"
            }
        }
    }

    MouseArea {
        anchors.fill: parent
        hoverEnabled: true
        onClicked: {
            expanded = !expanded
        }
        onEntered: {
            if (expanded) {
                collapseTimer.stop()
            }
        }
        onExited: {
            if (expanded) {
                collapseTimer.start()
            }
        }
    }

    Behavior on width {
        NumberAnimation { duration: 300; easing.type: Easing.OutCubic }
    }

}
