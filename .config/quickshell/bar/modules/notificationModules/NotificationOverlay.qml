import QtQuick
import QtQuick.Layouts
import Quickshell
import Quickshell.Wayland
import "../"

PanelWindow {
    id: notificationOverlay
    
    property var screen: null
    property var barWindow: null
    property bool overlayVisible: false
    property var notificationWidget: null
    
    // Use NotificationManager singleton
    property var notificationManager: NotificationManager
    
    signal overlayHidden()
    
    visible: overlayVisible
    color: "transparent"
    
    anchors {
        right: true
        top: true
        bottom: true
    }
    
    margins {
        right: 20
        top: 10
        bottom: 20
    }
    
    implicitWidth: 380
    focusable: true
    
    WlrLayershell.layer: WlrLayer.Overlay
    WlrLayershell.exclusiveZone: 0
    WlrLayershell.keyboardFocus: WlrKeyboardFocus.OnDemand
    
    Theme {
        id: theme
    }
    
    // Background with blur effect
    Rectangle {
        anchors.fill: parent
        radius: theme.largeBorderRadius
        color: theme.background
        
        // Pattern background
        Grid {
            id: patternGrid
            anchors.fill: parent
            anchors.margins: theme.largeBorderRadius // Respect the rounded corners
            clip: true
            
            property int patternSize: 150  // Larger pattern size for better visibility
            property int tilesX: Math.ceil(parent.width / patternSize)
            property int tilesY: Math.ceil(parent.height / patternSize)
            
            columns: tilesX
            rows: tilesY
            
            Repeater {
                model: patternGrid.tilesX * patternGrid.tilesY
                
                Image {
                    width: patternGrid.patternSize
                    height: patternGrid.patternSize
                    source: "file:///home/<USER>/.config/quickshell/bar/icons/pattern-sign.png"
                    fillMode: Image.PreserveAspectFit
                    smooth: true
                    cache: true
                    opacity: 0.35 // Subtle background pattern
                    
                    onStatusChanged: {
                        if (status === Image.Error) {
                            console.log("Pattern image failed to load:", source)
                            visible = false
                        }
                    }
                }
            }
        }
        
        // Header
        Rectangle {
            id: header
            anchors.top: parent.top
            anchors.left: parent.left
            anchors.right: parent.right
            height: 40
            radius: theme.largeBorderRadius
            color: theme.backgroundSecondary
            
            Rectangle {
                anchors.bottom: parent.bottom
                anchors.left: parent.left
                anchors.right: parent.right
                height: 12
                color: theme.backgroundSecondary
            }
            
            RowLayout {
                anchors.fill: parent
                anchors.margins: 12
                
                Text {
                    text: "Notifications"
                    color: theme.textPrimary
                    font.pixelSize: 14
                    font.weight: Font.Bold
                    Layout.fillWidth: true
                }
                
                Text {
                    text: notificationManager.list.length.toString()
                    color: theme.textSecondary
                    font.pixelSize: 12
                    font.family: "JetBrains Mono, monospace"
                }
                
                Rectangle {
                    width: 60
                    height: 20
                    radius: 10
                    color: theme.accent
                    
                    Text {
                        anchors.centerIn: parent
                        text: "Clear"
                        color: theme.background
                        font.pixelSize: 10
                        font.weight: Font.Bold
                    }
                    
                    MouseArea {
                        anchors.fill: parent
                        onClicked: {
                            notificationManager.discardAllNotifications()
                            // Don't hide overlay - just clear notifications
                        }
                    }
                }
            }
        }
        
        // Notification list
        ListView {
            id: notificationList
            anchors.top: header.bottom
            anchors.left: parent.left
            anchors.right: parent.right
            anchors.bottom: parent.bottom
            anchors.margins: 12
            anchors.topMargin: 8

            model: notificationManager.list.slice(0, 10)
            spacing: 8
            clip: true

            delegate: Rectangle {
                width: notificationList.width
                height: 90
                radius: theme.borderRadius
                color: theme.backgroundSecondary
                border.width: 1
                border.color: theme.border

                // App icon - square container (smaller than popup)
                Rectangle {
                    id: iconContainer
                    anchors.left: parent.left
                    anchors.top: parent.top
                    anchors.bottom: parent.bottom
                    anchors.margins: 12
                    width: height
                    color: "transparent"

                    Image {
                        id: appIcon
                        anchors.fill: parent
                        source: {
                            if (!modelData) return "file:///home/<USER>/.config/quickshell/bar/icons/mark-a.png"
                            
                            // Check if this is a screenshot notification
                            if (isScreenshotNotification()) {
                                var screenshotPath = extractScreenshotPath()
                                if (screenshotPath) {
                                    return "file://" + screenshotPath
                                }
                            }

                            // Use notification image first (for rich notifications like Spotify album art)
                            if (modelData.image && modelData.image !== "") {
                                return modelData.image.startsWith("file://") ? modelData.image : "file://" + modelData.image
                            }
                            
                            // Use app icon if available
                            if (modelData.appIcon && modelData.appIcon !== "") {
                                return modelData.appIcon.startsWith("file://") ? modelData.appIcon : "file://" + modelData.appIcon
                            }
                            
                            // Fallback to default icon
                            return "file:///home/<USER>/.config/quickshell/bar/icons/mark-a.png"
                        }
                        fillMode: isScreenshotNotification() ? Image.PreserveAspectCrop : Image.PreserveAspectFit
                        smooth: true
                        cache: false

                        onStatusChanged: {
                            if (status === Image.Error) {
                                source = "file://" + "/home/<USER>/.config/quickshell/bar/icons/mark-a.png"
                            }
                        }

                        function isScreenshotNotification() {
                            if (!modelData) return false
                            var summary = modelData.summary || ""
                            var body = modelData.body || ""
                            var appName = modelData.appName || ""

                            return summary.toLowerCase().includes("screenshot") ||
                                   body.toLowerCase().includes("screenshot") ||
                                   appName.toLowerCase().includes("screenshot") ||
                                   summary.toLowerCase().includes("saved")
                        }

                        function extractScreenshotPath() {
                            if (!modelData) return ""
                            var body = modelData.body || ""

                            var pathMatch = body.match(/\/[^\s]+\.(png|jpg|jpeg)/i)
                            if (pathMatch) {
                                return pathMatch[0]
                            }

                            if (body.includes("/Pictures/screenshots/")) {
                                var screenshotMatch = body.match(/\/Pictures\/screenshots\/[^\s]+/i)
                                if (screenshotMatch) {
                                    return screenshotMatch[0]
                                }
                            }

                            return ""
                        }
                    }
                }

                // Content area (similar to popup but smaller)
                Column {
                    anchors.left: iconContainer.right
                    anchors.right: parent.right
                    anchors.top: parent.top
                    anchors.bottom: parent.bottom
                    anchors.margins: 12
                    anchors.leftMargin: 8
                    spacing: 4

                    // Summary (title)
                    Text {
                        text: modelData.summary || ""
                        color: theme.textPrimary
                        font.pixelSize: 12
                        font.weight: Font.Bold
                        wrapMode: Text.WordWrap
                        width: parent.width
                        maximumLineCount: 1
                        elide: Text.ElideRight
                    }

                    // Body text
                    Text {
                        text: modelData.body || ""
                        color: theme.textSecondary
                        font.pixelSize: 10
                        wrapMode: Text.WordWrap
                        width: parent.width
                        maximumLineCount: 2
                        elide: Text.ElideRight
                    }

                    // App name
                    Text {
                        text: modelData.appName || ""
                        color: theme.textTertiary
                        font.pixelSize: 9
                        font.italic: true
                        elide: Text.ElideRight
                        width: parent.width
                    }
                }

                MouseArea {
                    anchors.fill: parent
                    onClicked: {
                        // Remove this notification from our history
                        notificationManager.discardNotification(modelData.notificationId)
                    }
                }
            }

            // Empty state
            Text {
                anchors.centerIn: parent
                text: "No notifications"
                color: theme.textSecondary
                font.pixelSize: 14
                visible: notificationList.count === 0
            }
        }
    }
    
    // Click outside to hide - similar to CalendarOverlay
    MouseArea {
        anchors.fill: parent
        propagateComposedEvents: true
        acceptedButtons: Qt.LeftButton | Qt.RightButton

        onClicked: function(mouse) {
            // Check if click is outside the main content area
            var clickX = mouse.x
            var clickY = mouse.y
            var contentLeft = parent.children[0].x  // Background rectangle
            var contentTop = parent.children[0].y
            var contentRight = parent.children[0].x + parent.children[0].width
            var contentBottom = parent.children[0].y + parent.children[0].height

            if (clickX < contentLeft || clickX > contentRight ||
                clickY < contentTop || clickY > contentBottom) {
                // Click outside content - hide overlay
                console.log("NotificationOverlay: Click outside content, hiding overlay")
                hideOverlay()
                mouse.accepted = true
            } else {
                // Click inside content - let content handle it
                mouse.accepted = false
            }
        }
    }
    
    function showOverlay() {
        console.log("NotificationOverlay: Showing overlay")
        overlayVisible = true
    }
    
    function hideOverlay() {
        console.log("NotificationOverlay: Hiding overlay")
        overlayVisible = false
        overlayHidden()
    }
    
    function formatTime(timestamp) {
        const now = Date.now()
        const diff = now - timestamp
        const minutes = Math.floor(diff / 60000)
        const hours = Math.floor(minutes / 60)
        const days = Math.floor(hours / 24)
        
        if (days > 0) return days + "d"
        if (hours > 0) return hours + "h"
        if (minutes > 0) return minutes + "m"
        return "now"
    }
    
    // Connect to NotificationManager signals for reactive updates
    Connections {
        target: notificationManager
        function onNotify(notification) {
            console.log("NotificationOverlay: Received notification from manager:", notification.summary)
        }
        
        function onDiscard(id) {
            console.log("NotificationOverlay: Notification discarded:", id)
        }
        
        function onDiscardAll() {
            console.log("NotificationOverlay: All notifications discarded")
        }
    }
    

    
    Component.onCompleted: {
        console.log("NotificationOverlay: Component loaded")
    }
}
