import QtQuick
import Quickshell
import Quickshell.Services.Notifications
import Quickshell.Wayland
import "../"

Item {
    id: notificationSystem

    Theme {
        id: theme
    }
    
    // Track active notification windows for stacking
    property var activeNotificationWindows: []
    property int notificationSpacing: 5
    property int notificationHeight: 130
    
    Component.onCompleted: {
        console.log("QuickShell notification system component created")
    }
    
    NotificationServer {
        id: notificationServer
        
        bodySupported: true
        bodyMarkupSupported: false
        actionsSupported: true
        persistenceSupported: true
        keepOnReload: true
        
        Component.onCompleted: {
            console.log("NotificationServer created with capabilities:")
            console.log("  - bodySupported:", bodySupported)
            console.log("  - actionsSupported:", actionsSupported)
            console.log("  - persistenceSupported:", persistenceSupported)
        }
        
        onNotification: function(notification) {
            console.log("===== NEW NOTIFICATION RECEIVED =====")
            console.log("Notification ID:", notification.id)
            console.log("App Name:", notification.appName)
            console.log("Summary:", notification.summary)
            console.log("Body:", notification.body)
            console.log("Urgency:", notification.urgency)
            console.log("=====================================")
            
            createNotificationPopup(notification)
        }
    }
    

    
    Component {
        id: notificationWindowComponent

        PanelWindow {
            id: notificationWindow

            Theme {
                id: theme
            }
            
            property var notificationData: null
            
            screen: Quickshell.screens[0]
            implicitWidth: 400
            implicitHeight: 120
            visible: true
            color: "transparent"
            
            WlrLayershell.layer: WlrLayer.Top
            
            anchors {
                top: true
                right: true
            }
            
            margins {
                top: getNotificationTopMargin()
                right: 10
            }
            
            function getNotificationTopMargin() {
                var barHeight = 30  // Bar implicit height
                var baseMargin = barHeight + 10  // Bar height + 10px spacing
                var index = notificationSystem.activeNotificationWindows.length
                var calculatedMargin = baseMargin + (index * (notificationSystem.notificationHeight + notificationSystem.notificationSpacing))
                console.log("Calculating top margin for notification", index, ":", calculatedMargin)
                return calculatedMargin
            }
            
            Rectangle {
                anchors.fill: parent
                color: theme.background
                border.color: theme.border
                border.width: 0
                radius: theme.borderRadius
                
                Row {
                    anchors.fill: parent
                    anchors.margins: 12
                    spacing: 12
                    
                    // Icon/Image section
                    Rectangle {
                        width: 64
                        height: parent.height
                        color: "transparent"
                        
                        Image {
                            id: notificationIcon
                            anchors.centerIn: parent
                            width: 64
                            height: parent.height - 8
                            source: getNotificationIcon()
                            fillMode: isScreenshot() ? Image.PreserveAspectCrop : Image.PreserveAspectFit
                            smooth: true
                            clip: true
                            
                            function getNotificationIcon() {
                                // For screenshots, try to show the actual screenshot image
                                if (isScreenshot()) {
                                    var imagePath = extractImagePath(notificationWindow.notificationData)
                                    if (imagePath) {
                                        console.log("Using screenshot image as icon:", imagePath)
                                        return "file://" + imagePath
                                    }
                                }
                                
                                // Use provided icon or fallback
                                if (notificationWindow.notificationData && notificationWindow.notificationData.icon && notificationWindow.notificationData.icon.length > 0) {
                                    return notificationWindow.notificationData.icon
                                }
                                return "../../icons/mark-a.png"
                            }
                            
                            function isScreenshot() {
                                return notificationWindow.notificationData && 
                                       (notificationWindow.notificationData.appName === "hyprshot" ||
                                        notificationWindow.notificationData.appName === "flameshot" ||
                                        (notificationWindow.notificationData.summary && 
                                         notificationWindow.notificationData.summary.toLowerCase().includes("screenshot")))
                            }
                            
                            function extractImagePath(notificationData) {
                                if (!notificationData) return null
                                
                                // Try to extract image path from body or summary
                                var text = (notificationData.body || "") + " " + (notificationData.summary || "")
                                console.log("Extracting image path from text:", text)
                                
                                // Look for common image file patterns with full paths
                                var imageRegex = /([\/\w\-\.~]+\.(png|jpg|jpeg|gif|bmp|webp))/gi
                                var match = imageRegex.exec(text)
                                
                                if (match) {
                                    console.log("Found image path:", match[1])
                                    return match[1]
                                }
                                
                                return null
                            }
                        }
                    }
                    
                    // Content section
                    Column {
                        width: parent.width - 76 - 12  // Total width - icon width - spacing
                        height: parent.height
                        spacing: 4
                        
                        Row {
                            width: parent.width
                            spacing: 8
                            
                            Rectangle {
                                width: 8
                                height: 8
                                radius: 4
                                color: getUrgencyColor(notificationWindow.notificationData ? notificationWindow.notificationData.urgency : 1)
                                anchors.verticalCenter: parent.verticalCenter
                            }
                            
                            Text {
                                text: notificationWindow.notificationData ? notificationWindow.notificationData.appName : "App"
                                color: theme.textSecondary
                                font.pixelSize: 12
                                font.weight: Font.Bold
                                font.family: "JetBrains Mono, monospace"
                            }
                        }
                        
                        Text {
                            width: parent.width
                            text: notificationWindow.notificationData ? notificationWindow.notificationData.summary : "Summary"
                            color: theme.textPrimary
                            font.pixelSize: 14
                            font.weight: Font.Bold
                            font.family: "JetBrains Mono, monospace"
                            wrapMode: Text.WordWrap
                            maximumLineCount: 2
                            elide: Text.ElideRight
                        }
                        
                        Text {
                            width: parent.width
                            text: notificationWindow.notificationData ? notificationWindow.notificationData.body : "Body"
                            color: theme.textSecondary
                            font.pixelSize: 11
                            font.family: "JetBrains Mono, monospace"
                            wrapMode: Text.WordWrap
                            maximumLineCount: 3
                            elide: Text.ElideRight
                            visible: text.length > 0
                        }
                        
                        // Screenshot indicator
                        Text {
                            width: parent.width
                            text: notificationIcon.isScreenshot() ? "Click to open in feh" : ""
                            color: theme.textTertiary
                            font.pixelSize: 9
                            font.family: "JetBrains Mono, monospace"
                            visible: notificationIcon.isScreenshot()
                        }
                    }
                }
                
                MouseArea {
                    anchors.fill: parent
                    onClicked: {
                        console.log("Notification clicked")
                        
                        // Check if this is a screenshot notification
                        if (notificationIcon.isScreenshot()) {
                            // Try to extract image path from the notification
                            var imagePath = notificationIcon.extractImagePath(notificationWindow.notificationData)
                            if (imagePath) {
                                console.log("Opening screenshot in feh:", imagePath)
                                Qt.createQmlObject(`
                                    import QtQuick
                                    import Quickshell.Io
                                    Process {
                                        command: ["feh", "${imagePath}"]
                                        running: true
                                    }
                                `, notificationWindow)
                            }
                        }
                        
                        removeNotificationWindow(notificationWindow)
                    }                    
                }
            }
            
            Component.onCompleted: {
                console.log("Individual notification window created for:", notificationData ? notificationData.summary : "unknown")
            }
        }
    }
    
    function getUrgencyColor(urgency) {
        switch (urgency) {
            case 0: return theme.statusInfo
            case 1: return theme.statusNeutral
            case 2: return theme.statusError
            default: return theme.statusInfo
        }
    }
    
    // Signal to notify other components about new notifications
    signal notificationReceived(var notificationData)
    
    function createNotificationPopup(notification) {
        console.log("createNotificationPopup called for:", notification.summary)
        console.log("Creating individual FloatingWindow for notification...")
        
        var notificationData = {
            id: notification.id || Math.random(),
            appName: notification.appName || "Unknown App",
            summary: notification.summary || "Notification",
            body: notification.body || "",
            urgency: notification.urgency || 1,
            icon: notification.icon || ""
        }
        
        // Emit signal so other components can track this notification
        var widgetNotificationData = {
            id: notificationData.id,
            appName: notificationData.appName,
            summary: notificationData.summary,
            body: notificationData.body,
            urgency: getUrgencyString(notificationData.urgency),
            expireTimeout: getTimeoutForUrgency(notificationData.urgency),
            timestamp: Date.now(),
            actions: [],
            icon: notificationData.icon
        }
        notificationReceived(widgetNotificationData)
        
        var notificationWindow = notificationWindowComponent.createObject(notificationSystem, {
            "notificationData": notificationData
        })
        
        if (notificationWindow) {
            console.log("Individual notification window created successfully for:", notificationData.summary)
            
            // Add to active notifications list for stacking
            activeNotificationWindows.push(notificationWindow)
            
            // Update the window's position now that it's in the array
            var barHeight = 30  // Bar implicit height
            var correctTopMargin = (barHeight + 10) + ((activeNotificationWindows.length - 1) * (notificationHeight + notificationSpacing))
            notificationWindow.margins.top = correctTopMargin
            console.log("Set notification", activeNotificationWindows.length - 1, "top margin to:", correctTopMargin)
            
            // Set up auto-hide timer
            var timeout = getTimeoutForUrgency(notificationData.urgency)
            var timer = Qt.createQmlObject(`
                import QtQuick
                Timer {
                    interval: ${timeout}
                    running: true
                    repeat: false
                }
            `, notificationSystem)
            
            timer.triggered.connect(function() {
                console.log("Auto-hiding notification window:", notificationData.summary)
                removeNotificationWindow(notificationWindow)
                timer.destroy()
            })
            
            // Connect destroy signal to remove from list
            notificationWindow.Component.destruction.connect(function() {
                removeNotificationWindow(notificationWindow)
            })
            
        } else {
            console.log("Failed to create individual notification window")
        }
    }
    
    function getTimeoutForUrgency(urgency) {
        switch (urgency) {
            case 0: return 3000
            case 1: return 5000
            case 2: return 8000
            default: return 5000
        }
    }
    
    function getUrgencyString(urgency) {
        switch (urgency) {
            case 0: return "low"
            case 1: return "normal"
            case 2: return "critical"
            default: return "normal"
        }
    }
    
    function removeNotificationWindow(notificationWindow) {
        // Remove from active notifications list
        var index = activeNotificationWindows.indexOf(notificationWindow)
        if (index > -1) {
            activeNotificationWindows.splice(index, 1)
            console.log("Removed notification from stack, remaining:", activeNotificationWindows.length)
            
            // Reposition remaining notifications
            repositionNotifications()
        }
        
        // Destroy the window if it still exists
        if (notificationWindow && typeof notificationWindow.destroy === "function") {
            notificationWindow.destroy()
        }
    }
    
    function repositionNotifications() {
        console.log("Repositioning", activeNotificationWindows.length, "notifications")
        
        for (var i = 0; i < activeNotificationWindows.length; i++) {
            var window = activeNotificationWindows[i]
            if (window) {
                var barHeight = 30  // Bar implicit height
                var newTopMargin = (barHeight + 10) + (i * (notificationHeight + notificationSpacing))
                
                // Animate the repositioning
                var animation = Qt.createQmlObject(`
                    import QtQuick
                    NumberAnimation {
                        target: window
                        property: "margins.top"
                        to: ${newTopMargin}
                        duration: 200
                        easing.type: Easing.OutQuad
                        running: true
                    }
                `, notificationSystem)
                
                // Clean up animation after it completes
                animation.finished.connect(function() {
                    animation.destroy()
                })
            }
        }
    }
    

    

}
