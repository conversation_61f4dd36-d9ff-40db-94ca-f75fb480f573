import QtQuick
import "../"

Rectangle {
    id: historyPopup
    
    property var notifications: []
    property var theme
    
    width: 400
    height: Math.min(notifications.length * 85 + 60, 900) // Max 10 notifications + padding
    color: theme.backgroundSecondary
    border.color: theme.border
    border.width: 1
    radius: theme.borderRadius
    
    signal closeRequested()
    signal notificationClicked(var notificationId)
    
    Column {
        anchors.fill: parent
        anchors.margins: 10
        spacing: 8
        
        Text {
            text: "Recent Notifications"
            color: theme.textPrimary
            font.pixelSize: 12
            font.weight: Font.Bold
            font.family: "JetBrains Mono, monospace"
        }
        
        Rectangle {
            width: parent.width
            height: 1
            color: theme.border
        }
        
        Repeater {
            model: notifications.slice(-10).reverse() // Show last 10 notifications, newest first
            
            Rectangle {
                width: parent.width
                height: 75
                color: "transparent"
                border.color: theme.border
                border.width: 1
                radius: theme.smallBorderRadius
                
                Row {
                    anchors.fill: parent
                    anchors.margins: 10
                    spacing: 10
                    
                    // Icon/Image section
                    Rectangle {
                        width: 48
                        height: parent.height
                        color: "transparent"
                        
                        Image {
                            anchors.centerIn: parent
                            width: 48
                            height: parent.height - 4
                            source: getNotificationIconForHistory(modelData)
                            fillMode: isScreenshot(modelData) ? Image.PreserveAspectCrop : Image.PreserveAspectFit
                            smooth: true
                            clip: true
                        }
                    }
                    
                    // Content
                    Column {
                        width: parent.width - 58 - 10  // Total width - icon width - spacing
                        height: parent.height
                        spacing: 2
                        
                        Row {
                            width: parent.width
                            spacing: 6
                            
                            Rectangle {
                                width: 6
                                height: 6
                                radius: 3
                                color: getUrgencyColor(modelData.urgency)
                                anchors.verticalCenter: parent.verticalCenter
                            }
                            
                            Text {
                                text: modelData.appName || "Unknown"
                                color: theme.textSecondary
                                font.pixelSize: 10
                                font.family: "JetBrains Mono, monospace"
                                elide: Text.ElideRight
                                width: parent.width - 20
                            }
                        }
                        
                        Text {
                            text: modelData.summary || "Notification"
                            color: theme.textPrimary
                            font.pixelSize: 12
                            font.weight: Font.Bold
                            font.family: "JetBrains Mono, monospace"
                            elide: Text.ElideRight
                            width: parent.width
                        }
                        
                        Text {
                            text: modelData.body || ""
                            color: theme.textSecondary
                            font.pixelSize: 10
                            font.family: "JetBrains Mono, monospace"
                            elide: Text.ElideRight
                            width: parent.width
                            visible: text.length > 0
                        }
                        
                        Row {
                            width: parent.width
                            spacing: 8
                            
                            Text {
                                text: formatTimestamp(modelData.timestamp)
                                color: theme.textTertiary
                                font.pixelSize: 9
                                font.family: "JetBrains Mono, monospace"
                            }
                            
                            Text {
                                text: isScreenshot(modelData) ? "📷 Click to open" : ""
                                color: theme.statusInfo
                                font.pixelSize: 8
                                font.family: "JetBrains Mono, monospace"
                                visible: isScreenshot(modelData)
                            }
                        }
                    }
                }
                
                MouseArea {
                    anchors.fill: parent
                    hoverEnabled: true
                    
                    onClicked: {
                        // Check if this is a screenshot notification
                        if (isScreenshot(modelData)) {
                            var imagePath = extractImagePath(modelData)
                            if (imagePath) {
                                console.log("Opening screenshot from history in feh:", imagePath)
                                Qt.createQmlObject(`
                                    import QtQuick
                                    import Quickshell.Io
                                    Process {
                                        command: ["feh", "${imagePath}"]
                                        running: true
                                    }
                                `, historyPopup)
                            }
                        }
                        
                        notificationClicked(modelData.id)
                    }
                    
                    onEntered: {
                        parent.color = theme.hoverBackground
                    }
                    
                    onExited: {
                        parent.color = "transparent"
                    }
                }
            }
        }
    }
    
    // Close button
    Rectangle {
        width: 20
        height: 20
        color: theme.statusError
        radius: 10
        anchors.top: parent.top
        anchors.right: parent.right
        anchors.margins: 8
        
        Text {
            text: "×"
            color: theme.textPrimary
            font.pixelSize: 14
            font.weight: Font.Bold
            anchors.centerIn: parent
        }
        
        MouseArea {
            anchors.fill: parent
            onClicked: closeRequested()
        }
    }
    
    function getNotificationIcon(iconPath) {
        if (iconPath && iconPath.length > 0) {
            return iconPath
        }
        return "../../icons/mark-a.png"
    }
    
    function getNotificationIconForHistory(notificationData) {
        // For screenshots, try to show the actual screenshot image
        if (isScreenshot(notificationData)) {
            var imagePath = extractImagePath(notificationData)
            if (imagePath) {
                console.log("History: Using screenshot image as icon:", imagePath)
                return "file://" + imagePath
            }
        }
        
        // Use provided icon or fallback
        if (notificationData && notificationData.icon && notificationData.icon.length > 0) {
            return notificationData.icon
        }
        return "../../icons/mark-a.png"
    }
    
    function getUrgencyColor(urgency) {
        switch (urgency) {
            case "low": return theme.statusInfo
            case "critical": return theme.statusError
            default: return theme.statusNeutral
        }
    }
    
    function formatTimestamp(timestamp) {
        var now = Date.now()
        var diff = now - timestamp
        var minutes = Math.floor(diff / 60000)
        var hours = Math.floor(minutes / 60)
        
        if (minutes < 1) return "now"
        if (minutes < 60) return minutes + "m ago"
        if (hours < 24) return hours + "h ago"
        return "1d+ ago"
    }
    
    function isScreenshot(notificationData) {
        return notificationData && 
               (notificationData.appName === "hyprshot" ||
                notificationData.appName === "flameshot" ||
                (notificationData.summary && 
                 notificationData.summary.toLowerCase().includes("screenshot")))
    }
    
    function extractImagePath(notificationData) {
        if (!notificationData) return null
        
        // Try to extract image path from body or summary
        var text = (notificationData.body || "") + " " + (notificationData.summary || "")
        console.log("History: Extracting image path from text:", text)
        
        // Look for common image file patterns with full paths
        var imageRegex = /([\/\w\-\.~]+\.(png|jpg|jpeg|gif|bmp|webp))/gi
        var match = imageRegex.exec(text)
        
        if (match) {
            console.log("History: Found image path:", match[1])
            return match[1]
        }
        
        return null
    }
}