[{"notificationId": 1, "actions": [], "appIcon": "", "appName": "TestApp", "body": "This notification will test the clear all functionality", "image": "", "summary": "Test Clear <PERSON>", "time": 1753951201242, "urgency": "1"}, {"notificationId": 2, "actions": [], "appIcon": "", "appName": "TestApp", "body": "This notification will test the clear all functionality", "image": "", "summary": "Test Clear <PERSON>", "time": 1753951202979, "urgency": "1"}, {"notificationId": 3, "actions": [], "appIcon": "", "appName": "TestApp", "body": "This notification will test the clear all functionality", "image": "", "summary": "Test Clear <PERSON>", "time": 1753951203406, "urgency": "1"}, {"notificationId": 4, "actions": [], "appIcon": "", "appName": "TestApp", "body": "This notification will test the clear all functionality", "image": "", "summary": "Test Clear <PERSON>", "time": 1753951203673, "urgency": "1"}, {"notificationId": 5, "actions": [], "appIcon": "/home/<USER>/Pictures/screenshots//2025-07-31-171326_hyprshot.png", "appName": "Hyprshot", "body": "Image saved in <i>/home/<USER>/Pictures/screenshots//2025-07-31-171326_hyprshot.png</i> and copied to the clipboard.", "image": "", "summary": "Screenshot saved", "time": 1753953209890, "urgency": "1"}, {"notificationId": 6, "actions": [], "appIcon": "/home/<USER>/Pictures/screenshots//2025-07-31-171505_hyprshot.png", "appName": "Hyprshot", "body": "Image saved in <i>/home/<USER>/Pictures/screenshots//2025-07-31-171505_hyprshot.png</i> and copied to the clipboard.", "image": "", "summary": "Screenshot saved", "time": 1753953308236, "urgency": "1"}, {"notificationId": 7, "actions": [], "appIcon": "/home/<USER>/Pictures/screenshots//2025-07-31-171545_hyprshot.png", "appName": "Hyprshot", "body": "Image saved in <i>/home/<USER>/Pictures/screenshots//2025-07-31-171545_hyprshot.png</i> and copied to the clipboard.", "image": "", "summary": "Screenshot saved", "time": 1753953347496, "urgency": "1"}, {"notificationId": 8, "actions": [], "appIcon": "/home/<USER>/Pictures/screenshots//2025-07-31-171927_hyprshot.png", "appName": "Hyprshot", "body": "Image saved in <i>/home/<USER>/Pictures/screenshots//2025-07-31-171927_hyprshot.png</i> and copied to the clipboard.", "image": "", "summary": "Screenshot saved", "time": 1753953573090, "urgency": "1"}, {"notificationId": 9, "actions": [], "appIcon": "/home/<USER>/Pictures/screenshots//2025-07-31-173016_hyprshot.png", "appName": "Hyprshot", "body": "Image saved in <i>/home/<USER>/Pictures/screenshots//2025-07-31-173016_hyprshot.png</i> and copied to the clipboard.", "image": "", "summary": "Screenshot saved", "time": 1753954221178, "urgency": "1"}, {"notificationId": 10, "actions": [], "appIcon": "/home/<USER>/Pictures/screenshots//2025-07-31-180825_hyprshot.png", "appName": "Hyprshot", "body": "Image saved in <i>/home/<USER>/Pictures/screenshots//2025-07-31-180825_hyprshot.png</i> and copied to the clipboard.", "image": "", "summary": "Screenshot saved", "time": 1753956506636, "urgency": "1"}, {"notificationId": 11, "actions": [], "appIcon": "", "appName": "TestApp", "body": "This notification will test the clear all functionality", "image": "", "summary": "Test Clear <PERSON>", "time": 1753957137054, "urgency": "1"}, {"notificationId": 12, "actions": [], "appIcon": "", "appName": "TestApp", "body": "This notification will test the clear all functionality", "image": "", "summary": "Test Clear <PERSON>", "time": 1753957138047, "urgency": "1"}, {"notificationId": 13, "actions": [], "appIcon": "", "appName": "TestApp", "body": "This notification will test the clear all functionality", "image": "", "summary": "Test Clear <PERSON>", "time": 1753957138393, "urgency": "1"}, {"notificationId": 14, "actions": [], "appIcon": "", "appName": "TestApp", "body": "This notification will test the clear all functionality", "image": "", "summary": "Test Clear <PERSON>", "time": 1753957138700, "urgency": "1"}, {"notificationId": 15, "actions": [], "appIcon": "", "appName": "TestApp", "body": "This notification will test the clear all functionality", "image": "", "summary": "Test Clear <PERSON>", "time": 1753957139080, "urgency": "1"}, {"notificationId": 16, "actions": [], "appIcon": "", "appName": "TestApp", "body": "This notification will test the clear all functionality", "image": "", "summary": "Test Clear <PERSON>", "time": 1753957139490, "urgency": "1"}, {"notificationId": 17, "actions": [], "appIcon": "", "appName": "TestApp", "body": "This notification will test the clear all functionality", "image": "", "summary": "Test Clear <PERSON>", "time": 1753957139853, "urgency": "1"}]