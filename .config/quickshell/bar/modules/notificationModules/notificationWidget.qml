import QtQuick
import Quickshell
import Quickshell.Services.Notifications
import Quickshell.Wayland
import "../"

Item {
    id: notificationWidget
    width: indicator.width
    height: 18

    property bool hasNotifications: false
    property int notificationCount: 0
    property string currentUrgency: "normal"
    property var activeNotifications: []
    property var activePopups: []

    Theme {
        id: theme
    }

    NotificationServer {
        id: notificationServer
        bodySupported: true
        bodyMarkupSupported: true
        bodyHyperlinksSupported: true
        bodyImagesSupported: true
        actionsSupported: true
        persistenceSupported: true
        actionIconsSupported: true
        imageSupported: true
        inlineReplySupported: false
        keepOnReload: true

        Component.onCompleted: {
            console.log("NotificationDaemon: Server created with full capabilities")
        }

        onNotification: function(notification) {
            console.log("NotificationDaemon: Received notification:", notification.summary)
            console.log("  - App:", notification.appName)
            console.log("  - Body:", notification.body)
            console.log("  - Icon:", notification.appIcon)
            console.log("  - Urgency:", notification.urgency)
            console.log("  - Expire:", notification.expireTimeout)

            notification.tracked = true
            activeNotifications.push(notification)
            updateNotificationCount()
            createNotificationPopup(notification)
        }
    }


    Component {
        id: notificationPopupComponent

        PanelWindow {
            id: popup

            property var notification: null
            property int popupIndex: 0

            screen: notificationWidget.parent ? notificationWidget.parent.screen : null
            color: "transparent"  // Like calendarOverlay

            width: 380
            height: 120

            anchors {
                right: true
                top: true
            }

            margins {
                right: 20
                top: 50 + (popupIndex * 140)
            }

            WlrLayershell.layer: WlrLayer.Overlay
            WlrLayershell.exclusiveZone: 0
            WlrLayershell.keyboardFocus: WlrKeyboardFocus.None

            visible: true

            Rectangle {
                anchors.fill: parent
                radius: theme.largeBorderRadius  // Use theme radius like calendarOverlay
                color: theme.background

                // App icon - square container using notification height
                Rectangle {
                    id: iconContainer
                    anchors.left: parent.left
                    anchors.top: parent.top
                    anchors.bottom: parent.bottom
                    anchors.margins: 16
                    width: height  // Make it square - width equals height
                    color: "transparent"

                    Image {
                        id: appIcon
                        anchors.fill: parent
                        source: {
                            // Check if this is a screenshot notification
                            if (popup.notification && isScreenshotNotification()) {
                                // Try to extract screenshot path from notification body
                                var screenshotPath = extractScreenshotPath()
                                if (screenshotPath) {
                                    return "file://" + screenshotPath
                                }
                            }

                            if (popup.notification && popup.notification.appIcon && popup.notification.appIcon !== "") {
                                return popup.notification.appIcon
                            }
                            // Always fallback to mark-a.png
                            return "file://" + "/home/<USER>/.config/quickshell/bar/icons/mark-a.png"
                        }
                        fillMode: isScreenshotNotification() ? Image.PreserveAspectCrop : Image.PreserveAspectFit
                        smooth: true
                        cache: false

                        // Debug icon loading
                        onStatusChanged: {
                            console.log("NotificationDaemon: Icon status:", status, "source:", source)
                            if (status === Image.Error) {
                                console.log("NotificationDaemon: Icon failed to load, trying mark-a.png")
                                source = "file://" + "/home/<USER>/.config/quickshell/bar/icons/mark-a.png"
                            }
                        }

                        // Functions for screenshot detection
                        function isScreenshotNotification() {
                            if (!popup.notification) return false
                            var summary = popup.notification.summary || ""
                            var body = popup.notification.body || ""
                            var appName = popup.notification.appName || ""

                            return summary.toLowerCase().includes("screenshot") ||
                                   body.toLowerCase().includes("screenshot") ||
                                   appName.toLowerCase().includes("screenshot") ||
                                   summary.toLowerCase().includes("saved")
                        }

                        function extractScreenshotPath() {
                            if (!popup.notification) return ""
                            var body = popup.notification.body || ""

                            // Look for file paths in the notification body
                            var pathMatch = body.match(/\/[^\s]+\.(png|jpg|jpeg)/i)
                            if (pathMatch) {
                                return pathMatch[0]
                            }

                            // Look for common screenshot paths
                            if (body.includes("/Pictures/screenshots/")) {
                                var screenshotMatch = body.match(/\/Pictures\/screenshots\/[^\s]+/i)
                                if (screenshotMatch) {
                                    return screenshotMatch[0]
                                }
                            }

                            return ""
                        }
                    }
                }

                // Content area
                Column {
                    anchors.left: iconContainer.right
                    anchors.right: parent.right
                    anchors.top: parent.top
                    anchors.bottom: parent.bottom
                    anchors.margins: 16
                    anchors.leftMargin: 12
                    spacing: 10

                    // Summary (title)
                    Text {
                        text: popup.notification ? popup.notification.summary : ""
                        color: theme.textPrimary
                        font.pixelSize: 14
                        font.weight: Font.Bold
                        wrapMode: Text.WordWrap
                        width: parent.width
                        maximumLineCount: 1
                        elide: Text.ElideRight
                    }

                    // Body text
                    Text {
                        text: popup.notification ? popup.notification.body : ""
                        color: theme.textSecondary
                        font.pixelSize: 12
                        wrapMode: Text.WordWrap
                        width: parent.width
                        maximumLineCount: 3
                        elide: Text.ElideRight
                    }

                    // App name
                    Text {
                        text: popup.notification ? popup.notification.appName : ""
                        color: theme.textTertiary
                        font.pixelSize: 10
                        font.italic: true
                    }
                }

                // Click to dismiss
                MouseArea {
                    anchors.fill: parent
                    onClicked: {
                        console.log("NotificationDaemon: Popup clicked, dismissing")
                        if (popup.notification) {
                            popup.notification.dismiss()
                        }
                        removePopup(popup)
                    }
                }
            }

            // Auto-hide timer - maximum 1 second
            Timer {
                id: autoHideTimer
                interval: 3000  // Always 1 second max
                running: true
                repeat: false
                onTriggered: {
                    console.log("NotificationDaemon: Auto-hiding popup after 1 second")
                    removePopup(popup)
                }
            }
        }
    }

    // Smart popup management
    property int maxPopupsOnScreen: 4

    function createNotificationPopup(notification) {
        console.log("NotificationDaemon: Creating popup for:", notification.summary)

        // Remove oldest popup if screen is full
        if (activePopups.length >= maxPopupsOnScreen) {
            console.log("NotificationDaemon: Screen full, removing oldest popup")
            const oldestPopup = activePopups.shift()
            if (oldestPopup) {
                oldestPopup.destroy()
            }
        }

        // Create new popup
        const popup = notificationPopupComponent.createObject(notificationWidget, {
            "notification": notification,
            "popupIndex": activePopups.length
        })

        if (popup) {
            activePopups.push(popup)
            repositionPopups()
            console.log("NotificationDaemon: Created popup, total:", activePopups.length)
        }
    }

    // Always show indicator - gray circle when no notifications, colored when has notifications
    Rectangle {
        id: indicator
        width: content.width + 8
        height: 18
        radius: 9
        color: hasNotifications ? theme.accent : "transparent"
        border.width: hasNotifications ? 1 : 0
        border.color: hasNotifications ? theme.border : "transparent"

        Row {
            id: content
            anchors.centerIn: parent
            spacing: 4

            Text {
                text: hasNotifications ? "●" : "○"
                color: hasNotifications ? theme.background : theme.textSecondary
                font.pixelSize: 12
                font.family: "JetBrains Mono, monospace"
                anchors.verticalCenter: parent.verticalCenter
            }

            Text {
                text: notificationCount > 0 ? notificationCount.toString() : ""
                color: hasNotifications ? theme.background : theme.textPrimary
                font.pixelSize: 10
                font.family: "JetBrains Mono, monospace"
                anchors.verticalCenter: parent.verticalCenter
                visible: notificationCount > 0
            }
        }

        MouseArea {
            anchors.fill: parent
            acceptedButtons: Qt.LeftButton | Qt.RightButton

            onClicked: function(mouse) {
                if (mouse.button === Qt.LeftButton) {
                    // Show notification overlay
                    showNotificationOverlay()
                } else if (mouse.button === Qt.RightButton) {
                    clearAllNotifications()
                }
            }
        }

        Behavior on width {
            NumberAnimation { duration: 200; easing.type: Easing.OutCubic }
        }

        Behavior on height {
            NumberAnimation { duration: 200; easing.type: Easing.OutCubic }
        }
    }

    // Notification overlay signal
    signal showNotificationOverlayRequested()
    signal hideNotificationOverlayRequested()

    function showNotificationOverlay() {
        console.log("NotificationWidget: Showing notification overlay")
        showNotificationOverlayRequested()
    }

    function removePopup(popup) {
        const index = activePopups.indexOf(popup)
        if (index > -1) {
            activePopups.splice(index, 1)
            if (popup && popup.destroy) {
                popup.destroy()
            }
            repositionPopups()
            console.log("NotificationDaemon: Removed popup, remaining:", activePopups.length)
        }
    }

    function repositionPopups() {
        for (let i = 0; i < activePopups.length; i++) {
            if (activePopups[i] && activePopups[i].margins) {
                activePopups[i].popupIndex = i
                // Update position for PanelWindow
                activePopups[i].margins.top = 50 + (i * 140)
            }
        }
    }

    function removeNotificationFromList(notificationId) {
        activeNotifications = activeNotifications.filter(n => n.id !== notificationId)
        updateNotificationCount()
    }

    function updateNotificationCount() {
        // Use the server's tracked notifications ObjectModel
        if (notificationServer.trackedNotifications) {
            notificationCount = notificationServer.trackedNotifications.values.length
            hasNotifications = notificationCount > 0

            // Update urgency based on tracked notifications
            let highestUrgency = "low"
            const notifications = notificationServer.trackedNotifications.values
            for (let i = 0; i < notifications.length; i++) {
                const notification = notifications[i]
                if (notification.urgency === 2) { // Critical
                    highestUrgency = "critical"
                    break
                } else if (notification.urgency === 1 && highestUrgency === "low") { // Normal
                    highestUrgency = "normal"
                }
            }
            currentUrgency = highestUrgency
        } else {
            notificationCount = 0
            hasNotifications = false
            currentUrgency = "low"
        }

        console.log("NotificationDaemon: Updated count:", notificationCount, "urgency:", currentUrgency)
    }

    function clearAllNotifications() {
        console.log("NotificationDaemon: Clearing all notifications")

        // Dismiss all tracked notifications
        if (notificationServer.trackedNotifications) {
            const notifications = notificationServer.trackedNotifications.values
            for (let i = notifications.length - 1; i >= 0; i--) {
                notifications[i].dismiss()
            }
        }

        // Clear all popups
        for (const popup of activePopups) {
            if (popup && popup.destroy) {
                popup.destroy()
            }
        }
        activePopups = []

        updateNotificationCount()
    }

    // Monitor tracked notifications changes
    Connections {
        target: notificationServer
        function onTrackedNotificationsChanged() {
            updateNotificationCount()
        }
    }

    Component.onCompleted: {
        console.log("NotificationDaemon: Notification daemon widget loaded")
    }
}