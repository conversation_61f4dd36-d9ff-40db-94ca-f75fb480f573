import QtQuick
import QtQuick.Layouts
import Quickshell
import Quickshell.Wayland
import Quickshell.Services.Mpris
import Quickshell.Widgets
import "../"
import "."

PanelWindow {
    id: mprisOverlay

    property var screen
    property var barWindow
    property bool overlayVisible: false
    

    property real baseScreenWidth: screen ? screen.width : 1920
    property real baseScreenHeight: screen ? screen.height : 1080
    property real scaleFactor: Math.min(baseScreenWidth / 1920, baseScreenHeight / 1080)
    property int overlayWidth: Math.max(280, Math.min(480, baseScreenWidth * 0.25))
    property int overlayHeight: Math.max(140, Math.min(180, baseScreenHeight * 0.15))

    signal overlayHidden()
    signal overlayShown()
    signal trackChanged(string title, string artist, string album)
    signal playbackStateChanged(int state)

    readonly property var activePlayer: MprisService.activePlayer
    readonly property string cleanedTitle: activePlayer?.trackTitle || "No media"

    screen: Quickshell.screens[0]
    visible: overlayVisible && activePlayer !== null
    color: "transparent"
    implicitWidth: overlayWidth
    implicitHeight: overlayHeight

    anchors {
        top: true
        left: false
        right: false
        bottom: false
    }
    
    margins {
        top: 8
        left: 4
        right: 4
        bottom: 0
    }

    WlrLayershell.layer: WlrLayer.Top
    WlrLayershell.exclusiveZone: 0

    Theme {
        id: theme
    }


    Timer {
        running: activePlayer?.playbackState == MprisPlaybackState.Playing
        interval: 1000
        repeat: true
        onTriggered: activePlayer.positionChanged()
    }

    function formatTime(timeValue) {
        if (timeValue === null || timeValue === undefined || timeValue <= 0) {
            return "0:00"
        }
        
        var seconds
        if (timeValue > 3600) {
            seconds = Math.floor(timeValue / 1000000)
        } else if (timeValue > 1000 && timeValue < 3600) {
            var asMilliseconds = Math.floor(timeValue / 1000)
            var asSeconds = Math.floor(timeValue)
            if (asMilliseconds < 3600 && asSeconds > 3600) {
                seconds = asMilliseconds
            } else {
                seconds = asSeconds
            }
        } else {
            seconds = Math.floor(timeValue)
        }
        
        if (seconds < 0) seconds = 0
        if (seconds > 86400) {
            seconds = Math.floor(timeValue / 1000000)
        }
        
        var mins = Math.floor(seconds / 60)
        var secs = seconds % 60
        
        if (mins >= 60) {
            var hours = Math.floor(mins / 60)
            mins = mins % 60
            return hours + ":" + (mins < 10 ? "0" : "") + mins + ":" + (secs < 10 ? "0" : "") + secs
        }
        
        return mins + ":" + (secs < 10 ? "0" : "") + secs
    }


    Rectangle {
        id: backgroundRect
        anchors.fill: parent
        color: theme.background
        radius: theme.largeBorderRadius
        opacity: overlayVisible ? 1.0 : 0.0

        Behavior on opacity {
            NumberAnimation { 
                duration: theme.slowAnimationDuration
                easing.type: Easing.OutCubic 
            }
        }
    }

    MouseArea {
        anchors.fill: parent
        acceptedButtons: Qt.MiddleButton | Qt.BackButton | Qt.ForwardButton | Qt.RightButton | Qt.LeftButton
        onPressed: (event) => {
            if (event.button === Qt.MiddleButton) {
                MprisService.togglePlaying()
            } else if (event.button === Qt.BackButton) {
                MprisService.previous()
            } else if (event.button === Qt.ForwardButton || event.button === Qt.RightButton) {
                MprisService.next()
            } else if (event.button === Qt.LeftButton) {
                toggleOverlay()
            }
        }
    }


    Item {
        anchors.fill: parent
        anchors.margins: 16
        
        RowLayout {
            anchors.fill: parent
            spacing: 16
            

            Item {
                Layout.preferredWidth: 120
                Layout.preferredHeight: 120
                Layout.alignment: Qt.AlignLeft | Qt.AlignVCenter
                

                Item {
                    anchors.centerIn: parent
                    width: 100
                    height: 100
                    rotation: 45
                    

                    Rectangle {
                        anchors.fill: parent
                        color: "transparent"
                        border.color: theme.cyan
                        border.width: 2
                        

                        Rectangle {
                            anchors.fill: parent
                            anchors.margins: 4
                            color: "transparent"
                            border.color: theme.cyan
                            border.width: 1
                            

                            Rectangle {
                                anchors.fill: parent
                                anchors.margins: 4
                                color: theme.backgroundTertiary
                                clip: true
                                

                                Item {
                                    anchors.fill: parent
                                    rotation: -45
                                    
                                    Image {
                                        anchors.centerIn: parent
                                        width: parent.width * 1.414
                                        height: parent.height * 1.414
                                        source: activePlayer && activePlayer.trackArtUrl ? activePlayer.trackArtUrl : ""
                                        fillMode: Image.PreserveAspectCrop
                                        visible: activePlayer && activePlayer.trackArtUrl
                                    }
                                    
                                    Text {
                                        anchors.centerIn: parent
                                        text: "♪"
                                        color: theme.textPrimary
                                        font.pixelSize: 24
                                        font.family: "JetBrains Mono, monospace"
                                        font.weight: Font.Bold
                                        visible: !activePlayer || !activePlayer.trackArtUrl
                                    }
                                }
                            }
                        }
                    }
                }
            }
            

            ColumnLayout {
                Layout.fillWidth: true
                Layout.fillHeight: true
                spacing: 8
                

                Row {
                    Layout.fillWidth: true
                    Layout.alignment: Qt.AlignLeft
                    spacing: 0
                    
                    Text {
                        text: "『"
                        color: theme.textAccent
                        font.pixelSize: 16
                        font.family: "JetBrains Mono, monospace"
                        font.weight: Font.Bold
                    }
                    
                    Text {
                        text: activePlayer ? (activePlayer.trackTitle || "Unknown Title") : "No Track"
                        color: theme.white
                        font.pixelSize: 16
                        font.family: "JetBrains Mono, monospace"
                        font.weight: Font.Bold
                        elide: Text.ElideRight
                        width: Math.min(implicitWidth, parent.width - 60)
                    }
                    
                    Text {
                        text: "』"
                        color: theme.textAccent
                        font.pixelSize: 16
                        font.family: "JetBrains Mono, monospace"
                        font.weight: Font.Bold
                    }
                }
                

                Text {
                    Layout.fillWidth: true
                    text: activePlayer ? (activePlayer.trackArtist || "Unknown Artist") : "Unknown Artist"
                    color: theme.textSecondary
                    font.pixelSize: 12
                    font.family: "JetBrains Mono, monospace"
                    elide: Text.ElideRight
                }
                

                Item {
                    Layout.fillHeight: true
                }
                

                Item {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 60
                    

                    Rectangle {
                        id: playButton
                        anchors.top: parent.top
                        anchors.right: parent.right
                        width: 32
                        height: 32
                        radius: 8
                        color: playMouseArea.containsMouse ? theme.accent : theme.backgroundTertiary
                        border.color: theme.accent
                        border.width: 2

                        Text {
                            anchors.centerIn: parent
                            text: {
                                if (!activePlayer) return "󰐊"
                                return activePlayer.playbackState === MprisPlaybackState.Playing ? "󰏤" : "󰐊"
                            }
                            color: playMouseArea.containsMouse ? theme.backgroundPrimary : theme.textPrimary
                            font.pixelSize: 16
                            font.family: "JetBrains Mono Nerd Font, monospace"
                        }

                        MouseArea {
                            id: playMouseArea
                            anchors.fill: parent
                            hoverEnabled: true
                            onClicked: {
                                console.log("Play/pause button clicked")
                                MprisService.togglePlaying()
                            }
                        }

                        Behavior on color {
                            ColorAnimation { 
                                duration: theme.fastAnimationDuration
                                easing.type: Easing.OutCubic 
                            }
                        }
                    }
                    

                    ColumnLayout {
                        anchors.bottom: parent.bottom
                        anchors.left: parent.left
                        anchors.right: parent.right
                        spacing: 4

                    Text {
                        Layout.alignment: Qt.AlignLeft
                        text: {
                            if (!activePlayer) return "0:00 / 0:00"
                            var pos = activePlayer.position || 0
                            var len = activePlayer.length || 0
                            return formatTime(pos) + " / " + formatTime(len)
                        }
                        color: theme.textTertiary
                        font.pixelSize: 10
                        font.family: "JetBrains Mono, monospace"
                    }

                    RowLayout {
                        Layout.fillWidth: true
                        spacing: 8


                        Text {
                            text: "󰒮"
                            color: activePlayer && activePlayer.canGoPrevious ? theme.textPrimary : theme.textTertiary
                            font.pixelSize: 12
                            font.family: "JetBrains Mono Nerd Font, monospace"

                            MouseArea {
                                id: prevMouseArea
                                anchors.fill: parent
                                hoverEnabled: true
                                enabled: activePlayer && activePlayer.canGoPrevious
                                onClicked: {
                                    console.log("Previous button clicked")
                                    MprisService.previous()
                                }
                            }
                        }


                        Rectangle {
                            id: progressTrack
                            Layout.fillWidth: true
                            height: 6
                            radius: 3
                            color: theme.backgroundTertiary
                            
                            Rectangle {
                                id: progressFill
                                anchors.left: parent.left
                                anchors.top: parent.top
                                anchors.bottom: parent.bottom
                                width: {
                                    if (!activePlayer || !activePlayer.length || activePlayer.length <= 0) {
                                        return 0
                                    }
                                    var ratio = Math.max(0, Math.min(1, (activePlayer.position || 0) / activePlayer.length))
                                    return ratio * progressTrack.width
                                }
                                radius: parent.radius
                                color: theme.accent

                                Behavior on width {
                                    enabled: !seekMouseArea.pressed
                                    NumberAnimation { 
                                        duration: theme.fastAnimationDuration
                                        easing.type: Easing.OutCubic 
                                    }
                                }
                            }
                            
                            MouseArea {
                                id: seekMouseArea
                                anchors.fill: parent
                                enabled: activePlayer && activePlayer.canSeek
                                preventStealing: true
                                
                                onPressed: function(mouse) {
                                    console.log("Seekbar pressed at x:", mouse.x, "track width:", progressTrack.width)
                                    seekToPosition(mouse.x)
                                }
                                
                                onPositionChanged: function(mouse) {
                                    if (pressed) {
                                        console.log("Seekbar dragging at x:", mouse.x)
                                        seekToPosition(mouse.x)
                                    }
                                }
                                
                                onReleased: function(mouse) {
                                    console.log("Seekbar released")
                                }
                                
                                function seekToPosition(x) {
                                    console.log("seekToPosition called with x:", x)
                                    if (activePlayer && activePlayer.length > 0 && progressTrack.width > 0) {

                                        var boundedX = Math.max(0, Math.min(x, progressTrack.width))
                                        var ratio = boundedX / progressTrack.width
                                        var targetPosition = ratio * activePlayer.length
                                        var currentPosition = activePlayer.position || 0
                                        var offset = targetPosition - currentPosition
                                        
                                        console.log("Seeking - boundedX:", boundedX, "ratio:", ratio, "targetPosition:", targetPosition, "currentPosition:", currentPosition, "offset:", offset)
                                        
                                        try {

                                            activePlayer.seek(offset)
                                            console.log("Seek successful with offset:", offset)
                                        } catch (e) {
                                            console.log("Seek failed:", e)
                                        }
                                    } else {
                                        console.log("Cannot seek - activePlayer:", !!activePlayer, "length:", activePlayer ? activePlayer.length : "null", "track width:", progressTrack.width)
                                    }
                                }
                            }
                        }


                        Text {
                            text: "󰒭"
                            color: activePlayer && activePlayer.canGoNext ? theme.textPrimary : theme.textTertiary
                            font.pixelSize: 12
                            font.family: "JetBrains Mono Nerd Font, monospace"

                            MouseArea {
                                id: nextMouseArea
                                anchors.fill: parent
                                hoverEnabled: true
                                enabled: activePlayer && activePlayer.canGoNext
                                onClicked: {
                                    console.log("Next button clicked")
                                    MprisService.next()
                                }
                            }
                        }
                    }
                    }
                }
            }
        }
    }

    onOverlayVisibleChanged: {
        console.log("Overlay visibility changed to:", overlayVisible)
        if (overlayVisible) {
            overlayShown()
        } else {
            overlayHidden()
        }
    }

    function showOverlay() {
        console.log("showOverlay() called")
        console.log("  - Current overlayVisible:", overlayVisible)
        console.log("  - activePlayer:", activePlayer ? activePlayer.identity : "null")
        
        overlayVisible = true
        console.log("  - After setting: overlayVisible =", overlayVisible)
    }

    function hideOverlay() {
        console.log("hideOverlay() called")
        console.log("  - Current overlayVisible:", overlayVisible)
        
        overlayVisible = false
        console.log("  - After setting: overlayVisible =", overlayVisible)
    }

    function toggleOverlay() {
        console.log("toggleOverlay() called")
        console.log("  - Current state:", overlayVisible)
        console.log("  - activePlayer:", activePlayer ? activePlayer.identity : "null")
        console.log("  - Window visible:", visible)
        
        if (overlayVisible) {
            hideOverlay()
        } else {
            showOverlay()
        }
    }


}