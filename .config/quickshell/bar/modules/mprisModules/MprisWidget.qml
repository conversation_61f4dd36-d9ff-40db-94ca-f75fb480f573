import QtQuick
import QtQuick.Layouts
import Quickshell.Services.Mpris
import "../"

Rectangle {
    id: mprisWidget
    objectName: "mprisWidget"
    width: visible ? (mprisContent.width + 16) : 0
    height: 18
    radius: 9
    color: mouseArea.containsMouse ? Qt.rgba(theme.accent.r, theme.accent.g, theme.accent.b, 0.2) : "transparent"


    property int maxTrackInfoWidth: 200
    property bool showWhenNoPlayer: false


    visible: activePlayer !== null || showWhenNoPlayer


    signal showMprisOverlayRequested()
    signal hideMprisOverlayRequested()
    signal playerStateChanged(var player)
    signal trackChanged(string title, string artist)


    readonly property var activePlayer: {
        if (!Mpris.players || !Mpris.players.values) {
            return null
        }

        var playingPlayer = null
        var pausedPlayer = null
        var anyPlayer = null

        try {
            var playersList = Mpris.players.values

            for (var i = 0; i < playersList.length; i++) {
                var player = playersList[i]

                if (player && typeof player === "object" && player.identity) {
                    if (!anyPlayer) anyPlayer = player

                    if (player.playbackState === MprisPlaybackState.Playing) {
                        playingPlayer = player
                    } else if (player.playbackState === MprisPlaybackState.Paused) {
                        pausedPlayer = player
                    }
                }
            }
        } catch (e) {
            console.warn("Error detecting MPRIS players:", e)
        }

        return playingPlayer || pausedPlayer || anyPlayer
    }


    property var previousPlayer: null
    

    onActivePlayerChanged: {
        if (activePlayer !== previousPlayer) {
            previousPlayer = activePlayer
            playerStateChanged(activePlayer)
        }
    }


    Theme {
        id: theme
    }


    RowLayout {
        id: mprisContent
        anchors.centerIn: parent
        spacing: 8


        Text {
            id: playIcon
            text: {
                if (!activePlayer) return "󰝚"
                switch (activePlayer.playbackState) {
                    case MprisPlaybackState.Playing: return "󰏤"
                    case MprisPlaybackState.Paused: return "󰐊"
                    default: return "󰓛"
                }
            }
            color: theme.textPrimary
            font.pixelSize: 14
            font.family: "JetBrains Mono, monospace"
        }


        Text {
            id: trackInfo
            text: {
                if (!activePlayer) return "No media"
                
                var title = activePlayer.trackTitle || "Unknown"
                var artist = activePlayer.trackArtists && activePlayer.trackArtists.length > 0
                    ? activePlayer.trackArtists[0] : ""

                return artist ? title + " • " + artist : title
            }
            color: theme.textSecondary
            font.pixelSize: 11
            font.family: "JetBrains Mono, monospace"

            Layout.maximumWidth: maxTrackInfoWidth
            elide: Text.ElideRight
        }
    }


    MouseArea {
        id: mouseArea
        anchors.fill: parent
        acceptedButtons: Qt.LeftButton | Qt.RightButton
        hoverEnabled: true

        onClicked: function(mouse) {
            if (mouse.button === Qt.LeftButton) {
                showMprisOverlayRequested()
            } else if (mouse.button === Qt.RightButton && activePlayer) {
                togglePlayback()
            }
        }

        onEntered: {
        }

        onExited: {
        }
    }


    Behavior on color {
        ColorAnimation { 
            duration: theme.animationDuration
            easing.type: Easing.OutCubic
        }
    }

    Behavior on width {
        NumberAnimation { 
            duration: theme.slowAnimationDuration
            easing.type: Easing.OutCubic 
        }
    }


    function togglePlayback() {
        if (!activePlayer) return
        
        console.log("MprisWidget: togglePlayback called, using togglePlaying()")
        activePlayer.togglePlaying()
    }

    function playNext() {
        if (activePlayer && activePlayer.canGoNext) {
            activePlayer.next()
        }
    }

    function playPrevious() {
        if (activePlayer && activePlayer.canGoPrevious) {
            activePlayer.previous()
        }
    }

    function getCurrentTrackInfo() {
        if (!activePlayer) return { title: "", artist: "", album: "" }
        
        return {
            title: activePlayer.trackTitle || "",
            artist: activePlayer.trackArtists && activePlayer.trackArtists.length > 0 
                ? activePlayer.trackArtists[0] : "",
            album: activePlayer.trackAlbum || ""
        }
    }
}