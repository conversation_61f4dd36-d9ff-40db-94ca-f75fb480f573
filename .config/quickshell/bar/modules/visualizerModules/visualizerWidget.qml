import QtQuick
import QtQuick.Layouts
import Quickshell.Io
import ".."

Rectangle {
    id: visualizerWidget
    width: 100
    height: 18
    radius: 9
    color: "transparent"
    clip: false

    Theme {
        id: theme
    }

    property bool isActive: true
    property bool isSilent: true
    property int silenceCounter: 0
    property var audioData: new Array(15).fill(0)
    property int frameTimeMs: Math.floor(1000 / 60)
    property int updateCounter: 0
    property bool isHovered: false

    // Mouse area for hover detection
    MouseArea {
        anchors.fill: parent
        hoverEnabled: true
        onEntered: isHovered = true
        onExited: isHovered = false
    }

    Process {
        id: cavaProcess
        command: ["/sbin/cava", "-p", "/home/<USER>/.config/quickshell/bar/modules/visualizerModules/cavaConfig"]
        running: isActive

        stdout: SplitParser {
            splitMarker: "\n"

            onRead: data => {
                processAudioData(data)
            }
        }

        onExited: {
            console.log("Cava process exited with code:", exitCode)
            // Restart the process if it exits unexpectedly
            if (isActive) {
                restartTimer.start()
            }
        }

        onStarted: {
            console.log("Cava process started successfully")
        }
    }

    // Timer to restart cava process if it crashes
    Timer {
        id: restartTimer
        interval: 1000
        onTriggered: {
            if (isActive && !cavaProcess.running) {
                console.log("Restarting cava process...")
                cavaProcess.running = true
            }
        }
    }



    Timer {
        id: updateTimer
        interval: frameTimeMs
        running: isActive
        repeat: true
        onTriggered: executeVisualization()
    }

    // Pulsing animation for when silent
    Timer {
        id: pulseTimer
        interval: 100
        running: isActive && isSilent
        repeat: true
        onTriggered: {
            if (isSilent && silenceCounter > 30) {
                generatePulseData()
            }
        }
    }

    Row {
        id: visualizerBars
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.bottom: parent.bottom
        anchors.bottomMargin: 1
        spacing: 1

        Repeater {
            id: barRepeater
            model: 15

            Rectangle {
                id: bar
                width: 5
                height: Math.max(2, Math.min(16, getBarHeight(index)))
                color: getBarColor(index)
                radius: 1
                anchors.bottom: parent.bottom

                // Veering right animation effect
                property real animationOffset: 0

                transform: Translate {
                    x: animationOffset
                    y: -animationOffset * 0.3
                }

                Behavior on height {
                    NumberAnimation {
                        duration: isHovered ? 150 : 100
                        easing.type: Easing.OutCubic
                    }
                }

                Behavior on color {
                    ColorAnimation {
                        duration: isHovered ? 200 : 150
                        easing.type: Easing.OutCubic
                    }
                }

                Behavior on animationOffset {
                    NumberAnimation {
                        duration: 200
                        easing.type: Easing.OutCubic
                    }
                }

                function getBarHeight(barIndex) {
                    // Force reactivity by using updateCounter
                    updateCounter
                    let level = audioData[barIndex] || 0
                    let baseHeight = level * 2 + 2

                    // Add hover effect
                    if (isHovered) {
                        baseHeight *= 1.2
                        // Create veering right effect
                        animationOffset = (barIndex * 0.5) + (level * 0.3)
                    } else {
                        animationOffset = 0
                    }

                    return baseHeight
                }

                function getBarColor(barIndex) {
                    let level = audioData[barIndex] || 0
                    let intensity = Math.min(1.0, level / 7.0)

                    if (isSilent && silenceCounter > 60) {
                        // Subtle pulsing when silent
                        let pulseAlpha = 0.1 + Math.sin(Date.now() / 500) * 0.1
                        return Qt.rgba(theme.accent.r, theme.accent.g, theme.accent.b, pulseAlpha)
                    }

                    // Enhanced colors when hovered
                    let baseAlpha = 0.3 + (intensity * 0.7)
                    if (isHovered) {
                        baseAlpha = Math.min(1.0, baseAlpha * 1.5)
                        // Add slight color shift for hover
                        return Qt.rgba(
                            Math.min(1.0, theme.accent.r * 1.1),
                            Math.min(1.0, theme.accent.g * 1.1),
                            Math.min(1.0, theme.accent.b * 1.1),
                            baseAlpha
                        )
                    }

                    return Qt.rgba(
                        theme.accent.r,
                        theme.accent.g,
                        theme.accent.b,
                        baseAlpha
                    )
                }
            }
        }
    }

    function processAudioData(data) {
        if (!data || data.length === 0) return

        let line = data.trim()
        if (line.length > 0) {
            let values = line.split(' ')
            let hasAudio = false

            if (values.length >= 15) {
                for (let i = 0; i < Math.min(15, values.length); i++) {
                    let level = parseInt(values[i]) || 0
                    level = Math.min(7, Math.max(0, level))
                    audioData[i] = level

                    if (level > 0) {
                        hasAudio = true
                    }
                }
                // Force UI update by incrementing counter
                updateCounter++
            }

            isSilent = !hasAudio
            if (!isSilent) {
                silenceCounter = 0
            } else {
                silenceCounter++
            }
        }
    }

    function generatePulseData() {
        // Generate subtle pulsing animation when silent
        for (let i = 0; i < 15; i++) {
            let pulse = Math.sin((Date.now() / 800 + i * 0.2)) * 0.5 + 0.5
            audioData[i] = Math.floor(pulse * 2)
        }
        updateCounter++
    }

    function generateMockData() {
        for (let i = 0; i < 15; i++) {
            let baseLevel = Math.sin((Date.now() / 1000 + i * 0.5)) * 3 + 3
            let randomVariation = (Math.random() - 0.5) * 2
            audioData[i] = Math.max(0, Math.min(7, Math.floor(baseLevel + randomVariation)))
        }
        isSilent = false
        silenceCounter = 0
        updateCounter++
    }

    function generateDemoData() {
        for (let i = 0; i < 15; i++) {
            let wave = Math.sin((Date.now() / 200 + i * 0.3)) * 2 + 2
            audioData[i] = Math.max(0, Math.min(7, Math.floor(wave)))
        }
        isSilent = false
        silenceCounter = 0
        updateCounter++
    }

    function executeVisualization() {
        if (isSilent && silenceCounter > 120) {
            // Gradually fade out bars when silent for too long
            for (let i = 0; i < audioData.length; i++) {
                audioData[i] = Math.max(0, audioData[i] * 0.95)
            }
            updateCounter++
        }
    }

    Component.onCompleted: {
        console.log("Visualizer widget initialized")
        for (let i = 0; i < 15; i++) {
            audioData[i] = 0
        }
        // Start with a subtle pulse to show the widget is alive
        generatePulseData()
    }

    Component.onDestruction: {
        console.log("Visualizer widget destroyed")
        cavaProcess.running = false
        updateTimer.running = false
        pulseTimer.running = false
    }
}