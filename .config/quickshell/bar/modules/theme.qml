import QtQuick

QtObject {
    id: theme

    readonly property color background: "#000000"
    readonly property color foreground: "#f2f4f8"
    readonly property color cursor: "#ffffff"
    readonly property color cursorText: "#000000"
    readonly property color selectionBackground: "#393939"
    readonly property color selectionForeground: "#161616"

    readonly property color black: "#000000"
    readonly property color cyan: "#3ddbd9"
    readonly property color blue: "#33b1ff"
    readonly property color magenta: "#ee5396"
    readonly property color green: "#42be65"
    readonly property color purple: "#6D61E3"
    readonly property color pink: "#ff7eb6"
    readonly property color white: "#f2f4f8"
    readonly property color brightBlack: "#161616"
    readonly property color brightCyan: "#3ddbd9"
    readonly property color brightBlue: "#33b1ff"
    readonly property color brightMagenta: "#ee5396"
    readonly property color brightGreen: "#42be65"
    readonly property color brightPurple: "#6D61E3"
    readonly property color brightPink: "#ff7eb6"
    readonly property color brightWhite: "#f2f4f8"

    readonly property color barBackground: background
    readonly property color barForeground: foreground

    readonly property color workspaceActive: cyan
    readonly property color workspaceInactive: "#666666"
    readonly property color workspaceEmpty: "#333333"
    readonly property color workspaceUrgent: magenta
    readonly property color workspaceOccupied: blue

    readonly property color indicatorNormal: blue
    readonly property color indicatorActive: green
    readonly property color indicatorWarning: "#ffa500"
    readonly property color indicatorCritical: magenta
    readonly property color indicatorMuted: "#444444"
    readonly property color indicatorDisabled: "#222222"

    readonly property color textPrimary: foreground
    readonly property color textSecondary: "#999999"
    readonly property color textTertiary: "#777777"
    readonly property color textAccent: cyan
    readonly property color textMuted: "#666666"
    readonly property color textHighlight: white

    readonly property color hoverBackground: selectionBackground
    readonly property color hoverForeground: selectionForeground
    readonly property color activeBackground: purple
    readonly property color activeForeground: white
    readonly property color focusOutline: cyan

    readonly property color statusGood: green
    readonly property color statusWarning: "#ffa500"
    readonly property color statusError: magenta
    readonly property color statusInfo: blue
    readonly property color statusNeutral: "#888888"

    readonly property color glowColor: cyan
    readonly property color shadowColor: Qt.rgba(0, 0, 0, 0.5)

    readonly property color backgroundTransparent: Qt.rgba(0, 0, 0, 0.8)
    readonly property color foregroundDim: Qt.rgba(0.949, 0.957, 0.973, 0.7)
    readonly property color overlayBackground: Qt.rgba(0, 0, 0, 0.9)
    readonly property color backgroundPrimary: background
    readonly property color backgroundSecondary: "#111111"
    readonly property color backgroundTertiary: "#222222"
    readonly property color border: "#333333"
    readonly property color accent: cyan

    readonly property int animationDuration: 200
    readonly property int fastAnimationDuration: 100
    readonly property int slowAnimationDuration: 400

    readonly property int borderRadius: 4
    readonly property int smallBorderRadius: 2
    readonly property int largeBorderRadius: 8
}
