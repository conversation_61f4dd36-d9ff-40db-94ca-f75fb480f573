import QtQuick
import QtQuick.Layouts
import Quickshell.Hyprland

Item {
    id: workspacesContainer

    property int screenIndex: 0
    property var workspaceRanges: [
        [1, 9]
    ]
    property var currentRange: screenIndex < workspaceRanges.length ?
        workspaceRanges[screenIndex] : [1, 10]

    Theme {
        id: theme
    }
    
    property int refreshCounter: 0

    property int activeWorkspaceCount: {
        refreshCounter

        var count = 0
        var workspaces = Hyprland.workspaces

        if (workspaces && workspaces.values) {
            var workspaceList = workspaces.values
            for (var i = 0; i < workspaceList.length; i++) {
                var workspace = workspaceList[i]
                var hasWindows = workspace.lastIpcObject && workspace.lastIpcObject.windows > 0
                var isFocused = workspace.id === Hyprland.focusedWorkspace?.id

                if (hasWindows || isFocused) {
                    count++
                }
            }
        }
        return count
    }

    function shouldShowWorkspace(workspace) {
        if (!workspace) return false

        var workspaceId = workspace.id
        var inRange = workspaceId >= currentRange[0] && workspaceId <= currentRange[1]
        if (!inRange) return false

        Hyprland.refreshWorkspaces()

        var hasWindows = workspace.lastIpcObject && workspace.lastIpcObject.windows > 0
        var isFocused = workspace.id === Hyprland.focusedWorkspace?.id
        return hasWindows || isFocused
    }
    
    width: workspaceRow.width
    height: 20
    
    property var workspaceIcons: {
        "1": "",
        "2": "󰈹",
        "3": "󰊖",
        "4": "",
        "5": "󰴓",
        "6": "",
        "7": "󰯙",
        "8": "",
        "9": "󰍳"
    }
    
    Row {
        id: workspaceRow
        anchors.centerIn: parent
        spacing: 4
        
        Text {
            id: leftBracket
            text: "『"
            color: theme.textAccent
            font.pixelSize: 16
            font.family: "JetBrains Mono Nerd Font, monospace"
            font.weight: Font.Bold
            anchors.verticalCenter: parent.verticalCenter

            opacity: activeWorkspaceCount > 0 ? 1.0 : 0.4

            scale: activeWorkspaceCount > 0 ? 1.0 : 0.9

            Behavior on opacity {
                NumberAnimation { duration: 400; easing.type: Easing.OutCubic }
            }

            Behavior on scale {
                NumberAnimation {
                    duration: 500
                    easing.type: Easing.OutBack
                    easing.overshoot: 1.2
                }
            }
        }
        
        Row {
            id: workspaceIndicators
            spacing: 6
            anchors.verticalCenter: parent.verticalCenter
            
            Behavior on width {
                NumberAnimation { 
                    duration: 400
                    easing.type: Easing.OutBack
                    easing.overshoot: 1.1
                }
            }
            
            Repeater {
                id: workspaceRepeater
                model: Hyprland.workspaces

                Text {
                    id: workspaceIndicator

                    visible: shouldShowWorkspace(modelData)
                    width: visible ? 16 : 0
                    height: 16

                    text: workspaceIcons[modelData.id.toString()] || modelData.id.toString()
                    color: {
                        if (modelData.id === Hyprland.focusedWorkspace?.id) {
                            return theme.textPrimary
                        } else {
                            return theme.textSecondary
                        }
                    }
                    font.pixelSize: 14
                    font.family: "JetBrains Mono Nerd Font, monospace"
                    font.weight: modelData.id === Hyprland.focusedWorkspace?.id ? Font.Bold : Font.Medium

                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                    
                    Rectangle {
                        id: glowEffect
                        anchors.centerIn: parent
                        width: 24
                        height: 24
                        radius: 12
                        color: "transparent"
                        border.width: (modelData.id === Hyprland.focusedWorkspace?.id && shouldShowWorkspace(modelData) && (modelData.lastIpcObject?.windows > 0)) ? 2 : 0
                        border.color: Qt.rgba(theme.workspaceActive.r, theme.workspaceActive.g, theme.workspaceActive.b, 0.6)
                        opacity: (modelData.id === Hyprland.focusedWorkspace?.id && shouldShowWorkspace(modelData) && (modelData.lastIpcObject?.windows > 0)) ? 1 : 0

                        SequentialAnimation on border.color {
                            running: modelData.id === Hyprland.focusedWorkspace?.id && shouldShowWorkspace(modelData) && (modelData.lastIpcObject?.windows > 0)
                            loops: Animation.Infinite
                            ColorAnimation {
                                to: Qt.rgba(theme.workspaceActive.r, theme.workspaceActive.g, theme.workspaceActive.b, 0.8)
                                duration: 1500
                                easing.type: Easing.InOutSine
                            }
                            ColorAnimation {
                                to: Qt.rgba(theme.workspaceActive.r, theme.workspaceActive.g, theme.workspaceActive.b, 0.4)
                                duration: 1500
                                easing.type: Easing.InOutSine
                            }
                            PauseAnimation { duration: 500 }
                        }

                        Behavior on opacity {
                            NumberAnimation { duration: 400; easing.type: Easing.OutCubic }
                        }

                        Behavior on border.width {
                            NumberAnimation { duration: 300; easing.type: Easing.OutCubic }
                        }
                    }
                    
                    SequentialAnimation on scale {
                        id: pulseAnimation
                        running: modelData.id === Hyprland.focusedWorkspace?.id && !mouseArea.containsMouse && shouldShowWorkspace(modelData) && (modelData.lastIpcObject?.windows > 0)
                        loops: Animation.Infinite
                        NumberAnimation {
                            to: 1.08
                            duration: 1200
                            easing.type: Easing.InOutSine
                        }
                        NumberAnimation {
                            to: 1.0
                            duration: 1200
                            easing.type: Easing.InOutSine
                        }
                        PauseAnimation { duration: 300 }
                    }
                    
                    Behavior on color {
                        ColorAnimation { duration: 250 }
                    }
                    
                    scale: {
                        if (mouseArea.containsMouse) {
                            return modelData.id === Hyprland.focusedWorkspace?.id ? 1.3 : 1.25
                        } else {
                            return 1.0
                        }
                    }

                    Behavior on scale {
                        NumberAnimation {
                            duration: 300
                            easing.type: Easing.OutBack
                            easing.overshoot: 1.4
                        }
                    }

                    rotation: {
                        if (mouseArea.containsMouse) {
                            return modelData.id === Hyprland.focusedWorkspace?.id ? 5 : 3
                        } else {
                            return 0
                        }
                    }

                    Behavior on rotation {
                        NumberAnimation {
                            duration: 250
                            easing.type: Easing.OutBack
                            easing.overshoot: 1.1
                        }
                    }
                    
                    MouseArea {
                        id: mouseArea
                        anchors.fill: parent
                        hoverEnabled: true
                        
                        onClicked: {
                            Hyprland.dispatch("workspace " + modelData.id)
                        }
                    }
                    
                    NumberAnimation {
                        id: entranceAnimation
                        target: workspaceIndicator
                        property: "scale"
                        from: 0
                        to: 1
                        duration: 500
                        easing.type: Easing.OutBack
                        easing.overshoot: 1.8
                    }
                    
                    Component.onCompleted: {
                        entranceAnimation.start()
                    }
                }
            }
        }
        
        Text {
            id: rightBracket
            text: "』"
            color: theme.textAccent
            font.pixelSize: 16
            font.family: "JetBrains Mono Nerd Font, monospace"
            font.weight: Font.Bold
            anchors.verticalCenter: parent.verticalCenter

            opacity: activeWorkspaceCount > 0 ? 1.0 : 0.4

            scale: activeWorkspaceCount > 0 ? 1.0 : 0.9

            Behavior on opacity {
                NumberAnimation { duration: 400; easing.type: Easing.OutCubic }
            }

            Behavior on scale {
                NumberAnimation {
                    duration: 500
                    easing.type: Easing.OutBack
                    easing.overshoot: 1.2
                }
            }
        }
    }
    
    PropertyAnimation {
        id: bracketExpandAnimation
        target: workspaceRow
        property: "spacing"
        duration: 500
        easing.type: Easing.OutBack
        easing.overshoot: 1.3
    }

    PropertyAnimation {
        id: containerScaleAnimation
        target: workspacesContainer
        property: "scale"
        duration: 600
        easing.type: Easing.OutElastic
        easing.amplitude: 1.0
        easing.period: 0.5
    }

    onActiveWorkspaceCountChanged: {
        bracketExpandAnimation.from = workspaceRow.spacing
        bracketExpandAnimation.to = activeWorkspaceCount > 0 ? 6 : 2
        bracketExpandAnimation.start()

        containerScaleAnimation.from = activeWorkspaceCount > 0 ? 0.95 : 1.0
        containerScaleAnimation.to = 1.0
        containerScaleAnimation.start()
    }

    Connections {
        target: Hyprland

        function onFocusedWorkspaceChanged() {
            workspacesContainer.forceRefresh()
        }
    }

    property var previousWorkspaceState: ({})

    function forceRefresh() {
        Hyprland.refreshWorkspaces()
        refreshCounter++
    }

    function hasWorkspaceStateChanged() {
        var currentState = {}
        var workspaces = Hyprland.workspaces

        if (workspaces && workspaces.values) {
            var workspaceList = workspaces.values
            for (var i = 0; i < workspaceList.length; i++) {
                var workspace = workspaceList[i]
                var windowCount = workspace.lastIpcObject ? workspace.lastIpcObject.windows : 0
                currentState[workspace.id] = {
                    windows: windowCount,
                    focused: workspace.id === Hyprland.focusedWorkspace?.id
                }
            }
        }

        var stateChanged = JSON.stringify(currentState) !== JSON.stringify(previousWorkspaceState)
        if (stateChanged) {
            previousWorkspaceState = currentState
        }

        return stateChanged
    }

    Timer {
        id: smartRefreshTimer
        interval: 100
        running: true
        repeat: true
        onTriggered: {
            if (workspacesContainer.hasWorkspaceStateChanged()) {
                workspacesContainer.forceRefresh()
            }
        }
    }

    Component.onCompleted: {
        forceRefresh()
        smartRefreshTimer.start()
    }
}
